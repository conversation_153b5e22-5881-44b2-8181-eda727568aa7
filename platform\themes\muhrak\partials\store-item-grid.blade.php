<a href="{{ $store->url }}">
    <div class="store-card">
        <div class="store-card-flag">
            @php
                $countries = ['pk','om','us', 'gb', 'de', 'fr', 'jp', 'kr', 'cn'];
                $randomCountry = $countries[array_rand($countries)];
            @endphp
            <img src="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.3/flags/4x3/{{ $store->country ? strtolower($store->country) : $randomCountry }}.svg">
        </div>
    <div class="store-image-container">
        {{-- <img src="{{ Theme::asset()->url('img/1.webp') }}" alt="RED store" class="store-image" height="130"> --}}
        {!! RvMedia::image($store->logo_square, $store->name, 'full', true , ['class'=> 'store-image','height'=> '130']) !!}
        <div class="store-logo">
        {{-- <img src="https://cdn.komachine.com/media/company-logo/ef2d58e6-46c7-48c0-b044-d7d413791e22.png" alt="RED Logo" width="100" height="33"> --}}
        {!! RvMedia::image($store->logo, $store->name, 'full', true,  ['width'=>'100', 'height'=> '33']) !!}
        </div>
    </div>
    <div class="store-info">
        <h3 class="store-name">{{ $store->name }}</h3>
        <p class="store-info-description">{{ $store->description }}</p>
    </div>
    </div>
</a>
