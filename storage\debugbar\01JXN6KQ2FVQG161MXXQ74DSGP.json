{"__meta": {"id": "01JXN6KQ2FVQG161MXXQ74DSGP", "datetime": "2025-06-13 17:50:12", "utime": **********.048804, "method": "GET", "uri": "/stores", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749837010.703918, "end": **********.048817, "duration": 1.3448989391326904, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749837010.703918, "relative_start": 0, "end": **********.526655, "relative_end": **********.526655, "duration": 0.****************, "duration_str": "823ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.526683, "relative_start": 0.****************, "end": **********.048819, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "522ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.546808, "relative_start": 0.****************, "end": **********.558704, "relative_end": **********.558704, "duration": 0.011895895004272461, "duration_str": "11.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.muhrak::views.marketplace.stores", "start": **********.615101, "relative_start": 0.****************, "end": **********.615101, "relative_end": **********.615101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::views.marketplace.includes.store-category-filter", "start": **********.615597, "relative_start": 0.****************, "end": **********.615597, "relative_end": **********.615597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::views.marketplace.includes.store-items", "start": **********.634397, "relative_start": 0.9304790496826172, "end": **********.634397, "relative_end": **********.634397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.634978, "relative_start": 0.9310600757598877, "end": **********.634978, "relative_end": **********.634978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.657449, "relative_start": 0.95353102684021, "end": **********.657449, "relative_end": **********.657449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.668209, "relative_start": 0.9642910957336426, "end": **********.668209, "relative_end": **********.668209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.674265, "relative_start": 0.9703469276428223, "end": **********.674265, "relative_end": **********.674265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.680224, "relative_start": 0.9763059616088867, "end": **********.680224, "relative_end": **********.680224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.68625, "relative_start": 0.9823319911956787, "end": **********.68625, "relative_end": **********.68625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.692448, "relative_start": 0.9885299205780029, "end": **********.692448, "relative_end": **********.692448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.700449, "relative_start": 0.9965310096740723, "end": **********.700449, "relative_end": **********.700449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.706502, "relative_start": 1.0025839805603027, "end": **********.706502, "relative_end": **********.706502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.71565, "relative_start": 1.0117321014404297, "end": **********.71565, "relative_end": **********.71565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.727162, "relative_start": 1.0232439041137695, "end": **********.727162, "relative_end": **********.727162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.732939, "relative_start": 1.0290210247039795, "end": **********.732939, "relative_end": **********.732939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.740758, "relative_start": 1.0368399620056152, "end": **********.740758, "relative_end": **********.740758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.746705, "relative_start": 1.0427870750427246, "end": **********.746705, "relative_end": **********.746705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.store-item-grid", "start": **********.752191, "relative_start": 1.0482730865478516, "end": **********.752191, "relative_end": **********.752191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::bootstrap-4", "start": **********.760406, "relative_start": 1.056488037109375, "end": **********.760406, "relative_end": **********.760406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::layouts.default", "start": **********.761488, "relative_start": 1.057569980621338, "end": **********.761488, "relative_end": **********.761488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.header", "start": **********.761874, "relative_start": 1.0579559803009033, "end": **********.761874, "relative_end": **********.761874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.header-meta", "start": **********.762673, "relative_start": 1.0587549209594727, "end": **********.762673, "relative_end": **********.762673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.773604, "relative_start": 1.069685935974121, "end": **********.773604, "relative_end": **********.773604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.menu", "start": **********.813951, "relative_start": 1.1100330352783203, "end": **********.813951, "relative_end": **********.813951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.language-switcher", "start": **********.84637, "relative_start": 1.1424520015716553, "end": **********.84637, "relative_end": **********.84637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.cart", "start": **********.848893, "relative_start": 1.144974946975708, "end": **********.848893, "relative_end": **********.848893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.header-mobile", "start": **********.851532, "relative_start": 1.1476140022277832, "end": **********.851532, "relative_end": **********.851532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.cart", "start": **********.856482, "relative_start": 1.1525640487670898, "end": **********.856482, "relative_end": **********.856482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.cart", "start": **********.857638, "relative_start": 1.1537199020385742, "end": **********.857638, "relative_end": **********.857638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.menu", "start": **********.859964, "relative_start": 1.156045913696289, "end": **********.859964, "relative_end": **********.859964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.breadcrumbs", "start": **********.896829, "relative_start": 1.19291090965271, "end": **********.896829, "relative_end": **********.896829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.footer", "start": **********.899893, "relative_start": 1.1959750652313232, "end": **********.899893, "relative_end": **********.899893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::partials.social-links", "start": **********.904724, "relative_start": 1.200805902481079, "end": **********.904724, "relative_end": **********.904724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::277876e4aa56b0dd9b93cb7a033b5300", "start": **********.91154, "relative_start": 1.2076220512390137, "end": **********.91154, "relative_end": **********.91154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::277876e4aa56b0dd9b93cb7a033b5300", "start": **********.91202, "relative_start": 1.2081019878387451, "end": **********.91202, "relative_end": **********.91202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fdacb5a55a51d46f52156d7762811cab", "start": **********.913073, "relative_start": 1.2091550827026367, "end": **********.913073, "relative_end": **********.913073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fdacb5a55a51d46f52156d7762811cab", "start": **********.91351, "relative_start": 1.2095921039581299, "end": **********.91351, "relative_end": **********.91351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d1dc42d53c0f2a61d6007854a9ef44", "start": **********.914482, "relative_start": 1.210564136505127, "end": **********.914482, "relative_end": **********.914482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d1dc42d53c0f2a61d6007854a9ef44", "start": **********.914919, "relative_start": 1.211000919342041, "end": **********.914919, "relative_end": **********.914919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d34315f88105bc978e35c4720a4d13a0", "start": **********.915859, "relative_start": 1.2119410037994385, "end": **********.915859, "relative_end": **********.915859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d34315f88105bc978e35c4720a4d13a0", "start": **********.91632, "relative_start": 1.212402105331421, "end": **********.91632, "relative_end": **********.91632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::/../widgets/custom-menu/templates.frontend", "start": **********.923409, "relative_start": 1.2194910049438477, "end": **********.923409, "relative_end": **********.923409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/menu::partials.default", "start": **********.930755, "relative_start": 1.226836919784546, "end": **********.930755, "relative_end": **********.930755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::/../widgets/custom-menu/templates.frontend", "start": **********.973184, "relative_start": 1.269266128540039, "end": **********.973184, "relative_end": **********.973184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/menu::partials.default", "start": **********.979453, "relative_start": 1.2755351066589355, "end": **********.979453, "relative_end": **********.979453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.muhrak::/../widgets/custom-menu/templates.frontend", "start": **********.010891, "relative_start": 1.3069729804992676, "end": **********.010891, "relative_end": **********.010891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/menu::partials.default", "start": **********.015944, "relative_start": 1.312026023864746, "end": **********.015944, "relative_end": **********.015944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.038328, "relative_start": 1.3344099521636963, "end": **********.038328, "relative_end": **********.038328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::toast-notification", "start": **********.043595, "relative_start": 1.339677095413208, "end": **********.043595, "relative_end": **********.043595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.044126, "relative_start": 1.3402080535888672, "end": **********.044126, "relative_end": **********.044126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.04629, "relative_start": 1.342371940612793, "end": **********.046476, "relative_end": **********.046476, "duration": 0.00018596649169921875, "duration_str": "186μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52432544, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 50, "nb_templates": 50, "templates": [{"name": "theme.muhrak::views.marketplace.stores", "param_count": null, "params": [], "start": **********.615074, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/views/marketplace/stores.blade.phptheme.muhrak::views.marketplace.stores", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fviews%2Fmarketplace%2Fstores.blade.php&line=1", "ajax": false, "filename": "stores.blade.php", "line": "?"}}, {"name": "theme.muhrak::views.marketplace.includes.store-category-filter", "param_count": null, "params": [], "start": **********.615576, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/views/marketplace/includes/store-category-filter.blade.phptheme.muhrak::views.marketplace.includes.store-category-filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fviews%2Fmarketplace%2Fincludes%2Fstore-category-filter.blade.php&line=1", "ajax": false, "filename": "store-category-filter.blade.php", "line": "?"}}, {"name": "theme.muhrak::views.marketplace.includes.store-items", "param_count": null, "params": [], "start": **********.634374, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/views/marketplace/includes/store-items.blade.phptheme.muhrak::views.marketplace.includes.store-items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fviews%2Fmarketplace%2Fincludes%2Fstore-items.blade.php&line=1", "ajax": false, "filename": "store-items.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.634957, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.657398, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.668176, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.674241, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.6802, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.686225, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.692424, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.700424, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.706476, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.715607, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.727138, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.73291, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.740732, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.746681, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.store-item-grid", "param_count": null, "params": [], "start": **********.75217, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/store-item-grid.blade.phptheme.muhrak::partials.store-item-grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fstore-item-grid.blade.php&line=1", "ajax": false, "filename": "store-item-grid.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.76038, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "theme.muhrak::layouts.default", "param_count": null, "params": [], "start": **********.761468, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/layouts/default.blade.phptheme.muhrak::layouts.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.header", "param_count": null, "params": [], "start": **********.761855, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/header.blade.phptheme.muhrak::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.header-meta", "param_count": null, "params": [], "start": **********.762653, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/header-meta.blade.phptheme.muhrak::partials.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}}, {"name": "packages/theme::partials.header", "param_count": null, "params": [], "start": **********.773582, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.menu", "param_count": null, "params": [], "start": **********.813929, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/menu.blade.phptheme.muhrak::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.language-switcher", "param_count": null, "params": [], "start": **********.846348, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/language-switcher.blade.phptheme.muhrak::partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.cart", "param_count": null, "params": [], "start": **********.848871, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/cart.blade.phptheme.muhrak::partials.cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fcart.blade.php&line=1", "ajax": false, "filename": "cart.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.header-mobile", "param_count": null, "params": [], "start": **********.851511, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/header-mobile.blade.phptheme.muhrak::partials.header-mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fheader-mobile.blade.php&line=1", "ajax": false, "filename": "header-mobile.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.cart", "param_count": null, "params": [], "start": **********.856442, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/cart.blade.phptheme.muhrak::partials.cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fcart.blade.php&line=1", "ajax": false, "filename": "cart.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.cart", "param_count": null, "params": [], "start": **********.857605, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/cart.blade.phptheme.muhrak::partials.cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fcart.blade.php&line=1", "ajax": false, "filename": "cart.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.menu", "param_count": null, "params": [], "start": **********.859944, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/menu.blade.phptheme.muhrak::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.breadcrumbs", "param_count": null, "params": [], "start": **********.896795, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/breadcrumbs.blade.phptheme.muhrak::partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.footer", "param_count": null, "params": [], "start": **********.899872, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/footer.blade.phptheme.muhrak::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "theme.muhrak::partials.social-links", "param_count": null, "params": [], "start": **********.904703, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/social-links.blade.phptheme.muhrak::partials.social-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fpartials%2Fsocial-links.blade.php&line=1", "ajax": false, "filename": "social-links.blade.php", "line": "?"}}, {"name": "__components::277876e4aa56b0dd9b93cb7a033b5300", "param_count": null, "params": [], "start": **********.911507, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/277876e4aa56b0dd9b93cb7a033b5300.blade.php__components::277876e4aa56b0dd9b93cb7a033b5300", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F277876e4aa56b0dd9b93cb7a033b5300.blade.php&line=1", "ajax": false, "filename": "277876e4aa56b0dd9b93cb7a033b5300.blade.php", "line": "?"}}, {"name": "__components::277876e4aa56b0dd9b93cb7a033b5300", "param_count": null, "params": [], "start": **********.911983, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/277876e4aa56b0dd9b93cb7a033b5300.blade.php__components::277876e4aa56b0dd9b93cb7a033b5300", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F277876e4aa56b0dd9b93cb7a033b5300.blade.php&line=1", "ajax": false, "filename": "277876e4aa56b0dd9b93cb7a033b5300.blade.php", "line": "?"}}, {"name": "__components::fdacb5a55a51d46f52156d7762811cab", "param_count": null, "params": [], "start": **********.913045, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fdacb5a55a51d46f52156d7762811cab.blade.php__components::fdacb5a55a51d46f52156d7762811cab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffdacb5a55a51d46f52156d7762811cab.blade.php&line=1", "ajax": false, "filename": "fdacb5a55a51d46f52156d7762811cab.blade.php", "line": "?"}}, {"name": "__components::fdacb5a55a51d46f52156d7762811cab", "param_count": null, "params": [], "start": **********.913483, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fdacb5a55a51d46f52156d7762811cab.blade.php__components::fdacb5a55a51d46f52156d7762811cab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffdacb5a55a51d46f52156d7762811cab.blade.php&line=1", "ajax": false, "filename": "fdacb5a55a51d46f52156d7762811cab.blade.php", "line": "?"}}, {"name": "__components::c5d1dc42d53c0f2a61d6007854a9ef44", "param_count": null, "params": [], "start": **********.914454, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c5d1dc42d53c0f2a61d6007854a9ef44.blade.php__components::c5d1dc42d53c0f2a61d6007854a9ef44", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc5d1dc42d53c0f2a61d6007854a9ef44.blade.php&line=1", "ajax": false, "filename": "c5d1dc42d53c0f2a61d6007854a9ef44.blade.php", "line": "?"}}, {"name": "__components::c5d1dc42d53c0f2a61d6007854a9ef44", "param_count": null, "params": [], "start": **********.914892, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c5d1dc42d53c0f2a61d6007854a9ef44.blade.php__components::c5d1dc42d53c0f2a61d6007854a9ef44", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc5d1dc42d53c0f2a61d6007854a9ef44.blade.php&line=1", "ajax": false, "filename": "c5d1dc42d53c0f2a61d6007854a9ef44.blade.php", "line": "?"}}, {"name": "__components::d34315f88105bc978e35c4720a4d13a0", "param_count": null, "params": [], "start": **********.915831, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d34315f88105bc978e35c4720a4d13a0.blade.php__components::d34315f88105bc978e35c4720a4d13a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd34315f88105bc978e35c4720a4d13a0.blade.php&line=1", "ajax": false, "filename": "d34315f88105bc978e35c4720a4d13a0.blade.php", "line": "?"}}, {"name": "__components::d34315f88105bc978e35c4720a4d13a0", "param_count": null, "params": [], "start": **********.916293, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d34315f88105bc978e35c4720a4d13a0.blade.php__components::d34315f88105bc978e35c4720a4d13a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd34315f88105bc978e35c4720a4d13a0.blade.php&line=1", "ajax": false, "filename": "d34315f88105bc978e35c4720a4d13a0.blade.php", "line": "?"}}, {"name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.923387, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.phptheme.muhrak::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}}, {"name": "packages/menu::partials.default", "param_count": null, "params": [], "start": **********.930732, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/menu/resources/views/partials/default.blade.phppackages/menu::partials.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fresources%2Fviews%2Fpartials%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.973162, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.phptheme.muhrak::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}}, {"name": "packages/menu::partials.default", "param_count": null, "params": [], "start": **********.979433, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/menu/resources/views/partials/default.blade.phppackages/menu::partials.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fresources%2Fviews%2Fpartials%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.010868, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.phptheme.muhrak::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fthemes%2Fmuhrak%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}}, {"name": "packages/menu::partials.default", "param_count": null, "params": [], "start": **********.015924, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/menu/resources/views/partials/default.blade.phppackages/menu::partials.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fresources%2Fviews%2Fpartials%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.038307, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "packages/theme::toast-notification", "param_count": null, "params": [], "start": **********.043572, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/theme/resources/views/toast-notification.blade.phppackages/theme::toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}}, {"name": "packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.044106, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}}]}, "queries": {"count": 21, "nb_statements": 21, "nb_visible_statements": 21, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.008339999999999998, "accumulated_duration_str": "8.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.575557, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 5.635}, {"sql": "select count(*) as aggregate from `mp_stores` where `status` = 'published' and exists (select * from `ec_customers` where `mp_stores`.`customer_id` = `ec_customers`.`id` and `is_vendor` = 1 and `vendor_verified_at` is not null)", "type": "query", "params": [], "bindings": ["published", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.590106, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PublicStoreController.php:62", "source": {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicStoreController.php&line=62", "ajax": false, "filename": "PublicStoreController.php", "line": "62"}, "connection": "muhrak", "explain": null, "start_percent": 5.635, "width_percent": 5.036}, {"sql": "select `mp_stores`.*, (select count(*) from `ec_products` where `mp_stores`.`id` = `ec_products`.`store_id` and `is_variation` = 0 and `status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `mp_stores` where `status` = 'published' and exists (select * from `ec_customers` where `mp_stores`.`customer_id` = `ec_customers`.`id` and `is_vendor` = 1 and `vendor_verified_at` is not null) order by `created_at` desc limit 15 offset 0", "type": "query", "params": [], "bindings": [0, "published", 1, "published", "published", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.591793, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.671, "width_percent": 5.875}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (396, 397, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.596516, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.547, "width_percent": 5.156}, {"sql": "select * from `ec_products` inner join `ec_reviews` on `ec_products`.`id` = `ec_reviews`.`product_id` where `ec_products`.`store_id` in (396, 397, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412) and (`ec_products`.`status` = 'published' and `ec_reviews`.`status` = 'published') and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": ["published", "published", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.603035, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.703, "width_percent": 6.595}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/themes/muhrak/config.php", "file": "D:\\laragon\\www\\muhrak\\platform\\themes\\muhrak\\config.php", "line": 113}], "start": **********.6097908, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 28.297, "width_percent": 4.916}, {"sql": "select * from `mp_categories` where `status` = 'published' order by `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 63}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.618677, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 33.213, "width_percent": 4.676}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (18, 19, 20, 21) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Category'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Category"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 63}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.620864, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 37.89, "width_percent": 2.998}, {"sql": "select * from `menus` where `status` = 'published' and exists (select * from `language_meta` where `menus`.`id` = `language_meta`.`reference_id` and `language_meta`.`reference_type` = 'Botble\\\\Menu\\\\Models\\\\Menu' and `lang_meta_code` = 'en_US' and `lang_meta_code` = 'en_US')", "type": "query", "params": [], "bindings": ["published", "Botble\\Menu\\Models\\Menu", "en_US", "en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 17, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 18, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 170}, {"index": 20, "namespace": "view", "name": "theme.muhrak::partials.header", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/header.blade.php", "line": 34}], "start": **********.7939398, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 40.887, "width_percent": 4.796}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.795845, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 45.683, "width_percent": 3.717}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.7988389, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 49.4, "width_percent": 2.638}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36) and `meta_boxes`.`reference_type` = 'Botble\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Botble\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.804162, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 52.038, "width_percent": 2.878}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.8090801, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 54.916, "width_percent": 3.237}, {"sql": "select * from `ec_customers` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "view", "name": "theme.muhrak::partials.header", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/partials/header.blade.php", "line": 76}], "start": **********.849617, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 58.153, "width_percent": 6.235}, {"sql": "select * from `widgets` where (`theme` = 'muhrak')", "type": "query", "params": [], "bindings": ["muhrak"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 92}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 79}, {"index": 18, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 65}, {"index": 20, "namespace": null, "name": "platform/packages/widget/helpers/helpers.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\widget\\helpers\\helpers.php", "line": 32}], "start": **********.9184499, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 64.388, "width_percent": 6.355}, {"sql": "select * from `pages` where `pages`.`id` in (3, 4, 5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.925714, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 70.743, "width_percent": 5.396}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (3, 4, 5, 7) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9277399, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 76.139, "width_percent": 4.676}, {"sql": "select * from `pages` where `pages`.`id` in (2, 10, 11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.975133, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 80.815, "width_percent": 6.235}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (2, 8, 10, 11) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9768891, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 87.05, "width_percent": 3.357}, {"sql": "select * from `pages` where `pages`.`id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.012622, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 90.408, "width_percent": 5.516}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (6) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.muhrak::/../widgets/custom-menu/templates.frontend", "file": "D:\\laragon\\www\\muhrak\\platform\\themes/muhrak/////widgets/custom-menu/templates/frontend.blade.php", "line": 3}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.014018, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 95.923, "width_percent": 4.077}]}, "models": {"data": {"Botble\\Slug\\Models\\Slug": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Menu\\Models\\MenuNode": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuNode.php&line=1", "ajax": false, "filename": "MenuNode.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Page\\Models\\Page": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Botble\\Widget\\Models\\Widget": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fwidget%2Fsrc%2FModels%2FWidget.php&line=1", "ajax": false, "filename": "Widget.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Category": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Botble\\Menu\\Models\\Menu": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Menu\\Models\\MenuLocation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuLocation.php&line=1", "ajax": false, "filename": "MenuLocation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 93, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/stores", "action_name": "public.stores", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\Fronts\\PublicStoreController@getStores", "uri": "GET stores", "controller": "Botble\\Marketplace\\Http\\Controllers\\Fronts\\PublicStoreController@getStores<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicStoreController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers\\Fronts", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicStoreController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/Fronts/PublicStoreController.php:26-65</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "1.35s", "peak_memory": "52MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1837417042 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837417042\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-156181411 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156181411\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132159216 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://muhrak.gc/stores?page=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRPNGZsQzA1NEN5K2xiRmx1a0RKWkE9PSIsInZhbHVlIjoiTVIxNy9yTUp1SVJjSkZhb0FTRjJWdWtHNVQ1am5ac3RnVEQxSmtERzIzcHhjdkxKUkNxakdVNUtaZVpnd1V0WXREV2g0TFp4ZTBrNUhNMkx5Zmw2c2tNZzBQS2t6UnNMdUd3S0RIUUtBL0lDcXp4UzJEcXExYXZZcUZxdmdPY2UiLCJtYWMiOiIzZDk1ODg0YTI5YjliNTkwNWNiZTY2YTdjM2JmMmFjMWFhNjI3YmU4ODE3ZDcxZTI3NjAxM2JmMjVkNGJkZmJiIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ik5Iclg0d1laRFJ2SEw3WXVvSXpmV1E9PSIsInZhbHVlIjoiNkJNMmdicTI4cGZNR3ZaLzVWcmU4SGkzanpoTmZBMzhTejdEV3RxcHNBTnZVVWdac1FQclBoZENBVzF5OHcybzhJZHpZWjlTU0ZHNzFBa2hMYzVsZXVMYjQ3QThtZGo3OUZaWk40bHR5aEl5MS9uenV6RFlxekovc0xrWlppclUiLCJtYWMiOiIwMGE2Yjc2NTBkZGI3YTdlZTY5YTlkMzI2YjVkNGE2ZjQzMDk3ODhjMDg2MTg2MzNlZDEzOWI0NjU5NTMyYzg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132159216\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-382209580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382209580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-772936728 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:50:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-05-09 12:24:53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">No</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772936728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://muhrak.gc/stores?page=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/stores", "action_name": "public.stores", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\Fronts\\PublicStoreController@getStores"}, "badge": null}}