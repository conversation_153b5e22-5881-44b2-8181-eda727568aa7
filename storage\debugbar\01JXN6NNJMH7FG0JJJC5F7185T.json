{"__meta": {"id": "01JXN6NNJMH7FG0JJJC5F7185T", "datetime": "2025-06-13 17:51:16", "utime": **********.053314, "method": "GET", "uri": "/admin/settings/media", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.166362, "end": **********.053337, "duration": 4.886975049972534, "duration_str": "4.89s", "measures": [{"label": "Booting", "start": **********.166362, "relative_start": 0, "end": **********.797185, "relative_end": **********.797185, "duration": 0.****************, "duration_str": "631ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.797195, "relative_start": 0.****************, "end": **********.053339, "relative_end": 1.9073486328125e-06, "duration": 4.***************, "duration_str": "4.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.8144, "relative_start": 0.****************, "end": **********.820409, "relative_end": **********.820409, "duration": 0.006009101867675781, "duration_str": "6.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/setting::forms.partials.action", "start": **********.88004, "relative_start": 0.****************, "end": **********.88004, "relative_end": **********.88004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::section.action.save", "start": **********.9749, "relative_start": 0.****************, "end": **********.9749, "relative_end": **********.9749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.161995, "relative_start": 0.9956328868865967, "end": **********.161995, "relative_end": **********.161995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.164478, "relative_start": 0.9981160163879395, "end": **********.164478, "relative_end": **********.164478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::section.action.index", "start": **********.164822, "relative_start": 0.998460054397583, "end": **********.164822, "relative_end": **********.164822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::partials.media.action-buttons", "start": **********.217959, "relative_start": 1.0515968799591064, "end": **********.217959, "relative_end": **********.217959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.374302, "relative_start": 1.207939863204956, "end": **********.374302, "relative_end": **********.374302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::forms.partials.action", "start": **********.375025, "relative_start": 1.208662986755371, "end": **********.375025, "relative_end": **********.375025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::section.action.save", "start": **********.375365, "relative_start": 1.20900297164917, "end": **********.375365, "relative_end": **********.375365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.375839, "relative_start": 1.2094769477844238, "end": **********.375839, "relative_end": **********.375839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.37653, "relative_start": 1.2101678848266602, "end": **********.37653, "relative_end": **********.37653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::section.action.index", "start": **********.376776, "relative_start": 1.210413932800293, "end": **********.376776, "relative_end": **********.376776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::partials.media.chunk-size-upload-field", "start": **********.504192, "relative_start": 1.3378300666809082, "end": **********.504192, "relative_end": **********.504192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837073.094481, "relative_start": 1.9281189441680908, "end": 1749837073.094481, "relative_end": 1749837073.094481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837073.239079, "relative_start": 2.0727169513702393, "end": 1749837073.239079, "relative_end": 1749837073.239079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837073.240263, "relative_start": 2.0739009380340576, "end": 1749837073.240263, "relative_end": 1749837073.240263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": 1749837073.240787, "relative_start": 2.074424982070923, "end": 1749837073.240787, "relative_end": 1749837073.240787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837073.242431, "relative_start": 2.076068878173828, "end": 1749837073.242431, "relative_end": 1749837073.242431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": 1749837073.242865, "relative_start": 2.076503038406372, "end": 1749837073.242865, "relative_end": 1749837073.242865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837073.24323, "relative_start": 2.0768680572509766, "end": 1749837073.24323, "relative_end": 1749837073.24323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": 1749837073.243626, "relative_start": 2.0772640705108643, "end": 1749837073.243626, "relative_end": 1749837073.243626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837073.24443, "relative_start": 2.0780680179595947, "end": 1749837073.24443, "relative_end": 1749837073.24443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": 1749837073.244825, "relative_start": 2.078462839126587, "end": 1749837073.244825, "relative_end": 1749837073.244825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837073.245154, "relative_start": 2.078791856765747, "end": 1749837073.245154, "relative_end": 1749837073.245154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.fieldset", "start": 1749837073.245508, "relative_start": 2.079145908355713, "end": 1749837073.245508, "relative_end": 1749837073.245508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837073.269702, "relative_start": 2.103339910507202, "end": 1749837073.269702, "relative_end": 1749837073.269702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837073.365397, "relative_start": 2.1990349292755127, "end": 1749837073.365397, "relative_end": 1749837073.365397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::partials.media.media-folders-can-add-watermark-field", "start": 1749837073.366517, "relative_start": 2.200155019760132, "end": 1749837073.366517, "relative_end": 1749837073.366517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.011239, "relative_start": 2.844877004623413, "end": 1749837074.011239, "relative_end": 1749837074.011239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.011775, "relative_start": 2.8454129695892334, "end": 1749837074.011775, "relative_end": 1749837074.011775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.013026, "relative_start": 2.8466639518737793, "end": 1749837074.013026, "relative_end": 1749837074.013026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.013439, "relative_start": 2.847076892852783, "end": 1749837074.013439, "relative_end": 1749837074.013439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.013885, "relative_start": 2.8475229740142822, "end": 1749837074.013885, "relative_end": 1749837074.013885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.01472, "relative_start": 2.848357915878296, "end": 1749837074.01472, "relative_end": 1749837074.01472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.015086, "relative_start": 2.848723888397217, "end": 1749837074.015086, "relative_end": 1749837074.015086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.015474, "relative_start": 2.849112033843994, "end": 1749837074.015474, "relative_end": 1749837074.015474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.016224, "relative_start": 2.8498618602752686, "end": 1749837074.016224, "relative_end": 1749837074.016224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.016561, "relative_start": 2.850198984146118, "end": 1749837074.016561, "relative_end": 1749837074.016561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.016917, "relative_start": 2.850554943084717, "end": 1749837074.016917, "relative_end": 1749837074.016917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.017658, "relative_start": 2.8512959480285645, "end": 1749837074.017658, "relative_end": 1749837074.017658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.018031, "relative_start": 2.8516688346862793, "end": 1749837074.018031, "relative_end": 1749837074.018031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.018432, "relative_start": 2.852069854736328, "end": 1749837074.018432, "relative_end": 1749837074.018432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.019168, "relative_start": 2.8528058528900146, "end": 1749837074.019168, "relative_end": 1749837074.019168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.019554, "relative_start": 2.85319185256958, "end": 1749837074.019554, "relative_end": 1749837074.019554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.019945, "relative_start": 2.8535828590393066, "end": 1749837074.019945, "relative_end": 1749837074.019945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.020779, "relative_start": 2.854416847229004, "end": 1749837074.020779, "relative_end": 1749837074.020779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.021119, "relative_start": 2.854757070541382, "end": 1749837074.021119, "relative_end": 1749837074.021119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.021471, "relative_start": 2.8551089763641357, "end": 1749837074.021471, "relative_end": 1749837074.021471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.022246, "relative_start": 2.855883836746216, "end": 1749837074.022246, "relative_end": 1749837074.022246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.022619, "relative_start": 2.8562569618225098, "end": 1749837074.022619, "relative_end": 1749837074.022619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.022987, "relative_start": 2.8566248416900635, "end": 1749837074.022987, "relative_end": 1749837074.022987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.024095, "relative_start": 2.8577330112457275, "end": 1749837074.024095, "relative_end": 1749837074.024095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.024507, "relative_start": 2.858144998550415, "end": 1749837074.024507, "relative_end": 1749837074.024507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.024867, "relative_start": 2.8585050106048584, "end": 1749837074.024867, "relative_end": 1749837074.024867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.025602, "relative_start": 2.8592400550842285, "end": 1749837074.025602, "relative_end": 1749837074.025602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.025965, "relative_start": 2.859602928161621, "end": 1749837074.025965, "relative_end": 1749837074.025965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.026346, "relative_start": 2.8599839210510254, "end": 1749837074.026346, "relative_end": 1749837074.026346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.027058, "relative_start": 2.8606958389282227, "end": 1749837074.027058, "relative_end": 1749837074.027058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.027409, "relative_start": 2.8610470294952393, "end": 1749837074.027409, "relative_end": 1749837074.027409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.027773, "relative_start": 2.8614108562469482, "end": 1749837074.027773, "relative_end": 1749837074.027773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.02867, "relative_start": 2.8623080253601074, "end": 1749837074.02867, "relative_end": 1749837074.02867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.02904, "relative_start": 2.862678050994873, "end": 1749837074.02904, "relative_end": 1749837074.02904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.029394, "relative_start": 2.8630318641662598, "end": 1749837074.029394, "relative_end": 1749837074.029394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.030248, "relative_start": 2.8638858795166016, "end": 1749837074.030248, "relative_end": 1749837074.030248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.030588, "relative_start": 2.8642258644104004, "end": 1749837074.030588, "relative_end": 1749837074.030588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.030954, "relative_start": 2.8645918369293213, "end": 1749837074.030954, "relative_end": 1749837074.030954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.031641, "relative_start": 2.865278959274292, "end": 1749837074.031641, "relative_end": 1749837074.031641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.031969, "relative_start": 2.8656070232391357, "end": 1749837074.031969, "relative_end": 1749837074.031969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.03231, "relative_start": 2.865947961807251, "end": 1749837074.03231, "relative_end": 1749837074.03231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.032959, "relative_start": 2.8665969371795654, "end": 1749837074.032959, "relative_end": 1749837074.032959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.033288, "relative_start": 2.8669259548187256, "end": 1749837074.033288, "relative_end": 1749837074.033288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.033623, "relative_start": 2.8672609329223633, "end": 1749837074.033623, "relative_end": 1749837074.033623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.034277, "relative_start": 2.867914915084839, "end": 1749837074.034277, "relative_end": 1749837074.034277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.034598, "relative_start": 2.8682360649108887, "end": 1749837074.034598, "relative_end": 1749837074.034598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.034946, "relative_start": 2.868583917617798, "end": 1749837074.034946, "relative_end": 1749837074.034946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.035669, "relative_start": 2.869307041168213, "end": 1749837074.035669, "relative_end": 1749837074.035669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.035994, "relative_start": 2.8696320056915283, "end": 1749837074.035994, "relative_end": 1749837074.035994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.036331, "relative_start": 2.869968891143799, "end": 1749837074.036331, "relative_end": 1749837074.036331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.037067, "relative_start": 2.8707048892974854, "end": 1749837074.037067, "relative_end": 1749837074.037067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.037413, "relative_start": 2.8710508346557617, "end": 1749837074.037413, "relative_end": 1749837074.037413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": 1749837074.037746, "relative_start": 2.8713839054107666, "end": 1749837074.037746, "relative_end": 1749837074.037746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.038401, "relative_start": 2.8720388412475586, "end": 1749837074.038401, "relative_end": 1749837074.038401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.038735, "relative_start": 2.87237286567688, "end": 1749837074.038735, "relative_end": 1749837074.038735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.fieldset", "start": 1749837074.039038, "relative_start": 2.872675895690918, "end": 1749837074.039038, "relative_end": 1749837074.039038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "start": 1749837074.03928, "relative_start": 2.872917890548706, "end": 1749837074.03928, "relative_end": 1749837074.03928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1749837074.039573, "relative_start": 2.873210906982422, "end": 1749837074.039573, "relative_end": 1749837074.039573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::includes.form-media-size-label", "start": 1749837074.043493, "relative_start": 2.8771309852600098, "end": 1749837074.043493, "relative_end": 1749837074.043493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.172804, "relative_start": 3.006442070007324, "end": 1749837074.172804, "relative_end": 1749837074.172804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::includes.form-media-size-label", "start": 1749837074.173735, "relative_start": 3.0073728561401367, "end": 1749837074.173735, "relative_end": 1749837074.173735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.174209, "relative_start": 3.0078470706939697, "end": 1749837074.174209, "relative_end": 1749837074.174209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::includes.form-media-size-label", "start": 1749837074.174886, "relative_start": 3.008523941040039, "end": 1749837074.174886, "relative_end": 1749837074.174886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.175293, "relative_start": 3.0089309215545654, "end": 1749837074.175293, "relative_end": 1749837074.175293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::includes.form-media-size-label", "start": 1749837074.175963, "relative_start": 3.009600877761841, "end": 1749837074.175963, "relative_end": 1749837074.175963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.17638, "relative_start": 3.0100178718566895, "end": 1749837074.17638, "relative_end": 1749837074.17638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": 1749837074.178699, "relative_start": 3.0123369693756104, "end": **********.051025, "relative_end": **********.051025, "duration": 1.8723258972167969, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/setting::media", "start": 1749837074.17941, "relative_start": 3.013047933578491, "end": 1749837074.17941, "relative_end": 1749837074.17941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/setting::forms.form-content-only", "start": 1749837074.30386, "relative_start": 3.137497901916504, "end": 1749837074.30386, "relative_end": 1749837074.30386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": 1749837074.428502, "relative_start": 3.2621400356292725, "end": 1749837074.428502, "relative_end": 1749837074.428502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.429445, "relative_start": 3.263082981109619, "end": 1749837074.429445, "relative_end": 1749837074.429445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.430334, "relative_start": 3.263972043991089, "end": 1749837074.430334, "relative_end": 1749837074.430334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.431015, "relative_start": 3.264652967453003, "end": 1749837074.431015, "relative_end": 1749837074.431015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.431475, "relative_start": 3.26511287689209, "end": 1749837074.431475, "relative_end": 1749837074.431475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.431945, "relative_start": 3.265583038330078, "end": 1749837074.431945, "relative_end": 1749837074.431945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.43288, "relative_start": 3.2665178775787354, "end": 1749837074.43288, "relative_end": 1749837074.43288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.43332, "relative_start": 3.266957998275757, "end": 1749837074.43332, "relative_end": 1749837074.43332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.433764, "relative_start": 3.267401933670044, "end": 1749837074.433764, "relative_end": 1749837074.433764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.434721, "relative_start": 3.2683589458465576, "end": 1749837074.434721, "relative_end": 1749837074.434721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.435396, "relative_start": 3.269033908843994, "end": 1749837074.435396, "relative_end": 1749837074.435396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.436073, "relative_start": 3.2697110176086426, "end": 1749837074.436073, "relative_end": 1749837074.436073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.436343, "relative_start": 3.2699809074401855, "end": 1749837074.436343, "relative_end": 1749837074.436343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.436651, "relative_start": 3.2702889442443848, "end": 1749837074.436651, "relative_end": 1749837074.436651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.437092, "relative_start": 3.2707300186157227, "end": 1749837074.437092, "relative_end": 1749837074.437092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.437509, "relative_start": 3.2711470127105713, "end": 1749837074.437509, "relative_end": 1749837074.437509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.437995, "relative_start": 3.2716329097747803, "end": 1749837074.437995, "relative_end": 1749837074.437995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.438254, "relative_start": 3.2718920707702637, "end": 1749837074.438254, "relative_end": 1749837074.438254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.438513, "relative_start": 3.272150993347168, "end": 1749837074.438513, "relative_end": 1749837074.438513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.438933, "relative_start": 3.272570848464966, "end": 1749837074.438933, "relative_end": 1749837074.438933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.440172, "relative_start": 3.2738099098205566, "end": 1749837074.440172, "relative_end": 1749837074.440172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.440576, "relative_start": 3.274214029312134, "end": 1749837074.440576, "relative_end": 1749837074.440576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.440967, "relative_start": 3.2746050357818604, "end": 1749837074.440967, "relative_end": 1749837074.440967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.441415, "relative_start": 3.275053024291992, "end": 1749837074.441415, "relative_end": 1749837074.441415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.441661, "relative_start": 3.275298833847046, "end": 1749837074.441661, "relative_end": 1749837074.441661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.441928, "relative_start": 3.2755658626556396, "end": 1749837074.441928, "relative_end": 1749837074.441928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.442233, "relative_start": 3.2758710384368896, "end": 1749837074.442233, "relative_end": 1749837074.442233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.442586, "relative_start": 3.27622389793396, "end": 1749837074.442586, "relative_end": 1749837074.442586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.442928, "relative_start": 3.2765660285949707, "end": 1749837074.442928, "relative_end": 1749837074.442928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.44328, "relative_start": 3.2769179344177246, "end": 1749837074.44328, "relative_end": 1749837074.44328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.443705, "relative_start": 3.2773430347442627, "end": 1749837074.443705, "relative_end": 1749837074.443705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.443946, "relative_start": 3.2775838375091553, "end": 1749837074.443946, "relative_end": 1749837074.443946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.444207, "relative_start": 3.2778449058532715, "end": 1749837074.444207, "relative_end": 1749837074.444207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.444504, "relative_start": 3.278141975402832, "end": 1749837074.444504, "relative_end": 1749837074.444504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.444854, "relative_start": 3.278491973876953, "end": 1749837074.444854, "relative_end": 1749837074.444854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.445195, "relative_start": 3.2788329124450684, "end": 1749837074.445195, "relative_end": 1749837074.445195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.445544, "relative_start": 3.279181957244873, "end": 1749837074.445544, "relative_end": 1749837074.445544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.445975, "relative_start": 3.2796130180358887, "end": 1749837074.445975, "relative_end": 1749837074.445975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.44621, "relative_start": 3.2798478603363037, "end": 1749837074.44621, "relative_end": 1749837074.44621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.446468, "relative_start": 3.2801060676574707, "end": 1749837074.446468, "relative_end": 1749837074.446468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.446766, "relative_start": 3.2804038524627686, "end": 1749837074.446766, "relative_end": 1749837074.446766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.447122, "relative_start": 3.2807600498199463, "end": 1749837074.447122, "relative_end": 1749837074.447122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.447456, "relative_start": 3.2810938358306885, "end": 1749837074.447456, "relative_end": 1749837074.447456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.447804, "relative_start": 3.2814419269561768, "end": 1749837074.447804, "relative_end": 1749837074.447804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.448233, "relative_start": 3.2818708419799805, "end": 1749837074.448233, "relative_end": 1749837074.448233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.448466, "relative_start": 3.282104015350342, "end": 1749837074.448466, "relative_end": 1749837074.448466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.448724, "relative_start": 3.2823619842529297, "end": 1749837074.448724, "relative_end": 1749837074.448724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.449137, "relative_start": 3.2827749252319336, "end": 1749837074.449137, "relative_end": 1749837074.449137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.449553, "relative_start": 3.283190965652466, "end": 1749837074.449553, "relative_end": 1749837074.449553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.44988, "relative_start": 3.283517837524414, "end": 1749837074.44988, "relative_end": 1749837074.44988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.450227, "relative_start": 3.283864974975586, "end": 1749837074.450227, "relative_end": 1749837074.450227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.450649, "relative_start": 3.2842869758605957, "end": 1749837074.450649, "relative_end": 1749837074.450649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.450871, "relative_start": 3.2845089435577393, "end": 1749837074.450871, "relative_end": 1749837074.450871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.451124, "relative_start": 3.284761905670166, "end": 1749837074.451124, "relative_end": 1749837074.451124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.451411, "relative_start": 3.2850489616394043, "end": 1749837074.451411, "relative_end": 1749837074.451411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.451754, "relative_start": 3.2853920459747314, "end": 1749837074.451754, "relative_end": 1749837074.451754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.452199, "relative_start": 3.285836935043335, "end": 1749837074.452199, "relative_end": 1749837074.452199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.45258, "relative_start": 3.2862179279327393, "end": 1749837074.45258, "relative_end": 1749837074.45258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.453009, "relative_start": 3.286646842956543, "end": 1749837074.453009, "relative_end": 1749837074.453009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.453233, "relative_start": 3.2868709564208984, "end": 1749837074.453233, "relative_end": 1749837074.453233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.453473, "relative_start": 3.2871110439300537, "end": 1749837074.453473, "relative_end": 1749837074.453473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.453775, "relative_start": 3.2874128818511963, "end": 1749837074.453775, "relative_end": 1749837074.453775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.454131, "relative_start": 3.287768840789795, "end": 1749837074.454131, "relative_end": 1749837074.454131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.454472, "relative_start": 3.2881100177764893, "end": 1749837074.454472, "relative_end": 1749837074.454472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.4548, "relative_start": 3.288437843322754, "end": 1749837074.4548, "relative_end": 1749837074.4548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.455086, "relative_start": 3.288723945617676, "end": 1749837074.455086, "relative_end": 1749837074.455086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.455506, "relative_start": 3.2891440391540527, "end": 1749837074.455506, "relative_end": 1749837074.455506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.455729, "relative_start": 3.2893669605255127, "end": 1749837074.455729, "relative_end": 1749837074.455729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.455974, "relative_start": 3.289612054824829, "end": 1749837074.455974, "relative_end": 1749837074.455974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.456268, "relative_start": 3.2899060249328613, "end": 1749837074.456268, "relative_end": 1749837074.456268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.456586, "relative_start": 3.2902238368988037, "end": 1749837074.456586, "relative_end": 1749837074.456586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.457054, "relative_start": 3.29069185256958, "end": 1749837074.457054, "relative_end": 1749837074.457054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.45728, "relative_start": 3.2909178733825684, "end": 1749837074.45728, "relative_end": 1749837074.45728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.457527, "relative_start": 3.2911648750305176, "end": 1749837074.457527, "relative_end": 1749837074.457527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.457814, "relative_start": 3.291451930999756, "end": 1749837074.457814, "relative_end": 1749837074.457814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.45813, "relative_start": 3.2917678356170654, "end": 1749837074.45813, "relative_end": 1749837074.45813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.458553, "relative_start": 3.2921910285949707, "end": 1749837074.458553, "relative_end": 1749837074.458553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.458773, "relative_start": 3.2924108505249023, "end": 1749837074.458773, "relative_end": 1749837074.458773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.459033, "relative_start": 3.292670965194702, "end": 1749837074.459033, "relative_end": 1749837074.459033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.459326, "relative_start": 3.292963981628418, "end": 1749837074.459326, "relative_end": 1749837074.459326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.459671, "relative_start": 3.293308973312378, "end": 1749837074.459671, "relative_end": 1749837074.459671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.459998, "relative_start": 3.293635845184326, "end": 1749837074.459998, "relative_end": 1749837074.459998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.460363, "relative_start": 3.2940008640289307, "end": 1749837074.460363, "relative_end": 1749837074.460363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.460785, "relative_start": 3.2944228649139404, "end": 1749837074.460785, "relative_end": 1749837074.460785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.461007, "relative_start": 3.294645071029663, "end": 1749837074.461007, "relative_end": 1749837074.461007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.461253, "relative_start": 3.294890880584717, "end": 1749837074.461253, "relative_end": 1749837074.461253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.461539, "relative_start": 3.2951769828796387, "end": 1749837074.461539, "relative_end": 1749837074.461539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.46188, "relative_start": 3.295517921447754, "end": 1749837074.46188, "relative_end": 1749837074.46188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.462206, "relative_start": 3.2958438396453857, "end": 1749837074.462206, "relative_end": 1749837074.462206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.462547, "relative_start": 3.29618501663208, "end": 1749837074.462547, "relative_end": 1749837074.462547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.462968, "relative_start": 3.2966060638427734, "end": 1749837074.462968, "relative_end": 1749837074.462968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.463189, "relative_start": 3.2968268394470215, "end": 1749837074.463189, "relative_end": 1749837074.463189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.463435, "relative_start": 3.2970728874206543, "end": 1749837074.463435, "relative_end": 1749837074.463435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.463721, "relative_start": 3.297358989715576, "end": 1749837074.463721, "relative_end": 1749837074.463721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.464063, "relative_start": 3.297700881958008, "end": 1749837074.464063, "relative_end": 1749837074.464063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.464444, "relative_start": 3.298081874847412, "end": 1749837074.464444, "relative_end": 1749837074.464444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.464829, "relative_start": 3.298466920852661, "end": 1749837074.464829, "relative_end": 1749837074.464829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.465229, "relative_start": 3.2988669872283936, "end": 1749837074.465229, "relative_end": 1749837074.465229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.46543, "relative_start": 3.299067974090576, "end": 1749837074.46543, "relative_end": 1749837074.46543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.465658, "relative_start": 3.2992959022521973, "end": 1749837074.465658, "relative_end": 1749837074.465658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.465925, "relative_start": 3.299562931060791, "end": 1749837074.465925, "relative_end": 1749837074.465925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.466249, "relative_start": 3.29988694190979, "end": 1749837074.466249, "relative_end": 1749837074.466249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.466554, "relative_start": 3.300191879272461, "end": 1749837074.466554, "relative_end": 1749837074.466554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.466873, "relative_start": 3.300510883331299, "end": 1749837074.466873, "relative_end": 1749837074.466873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.467274, "relative_start": 3.3009119033813477, "end": 1749837074.467274, "relative_end": 1749837074.467274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.467475, "relative_start": 3.3011128902435303, "end": 1749837074.467475, "relative_end": 1749837074.467475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.467818, "relative_start": 3.3014559745788574, "end": 1749837074.467818, "relative_end": 1749837074.467818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.468116, "relative_start": 3.3017539978027344, "end": 1749837074.468116, "relative_end": 1749837074.468116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.468457, "relative_start": 3.3020949363708496, "end": 1749837074.468457, "relative_end": 1749837074.468457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.468777, "relative_start": 3.302414894104004, "end": 1749837074.468777, "relative_end": 1749837074.468777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.469116, "relative_start": 3.3027539253234863, "end": 1749837074.469116, "relative_end": 1749837074.469116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.469627, "relative_start": 3.303264856338501, "end": 1749837074.469627, "relative_end": 1749837074.469627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.46987, "relative_start": 3.3035080432891846, "end": 1749837074.46987, "relative_end": 1749837074.46987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.470137, "relative_start": 3.303774833679199, "end": 1749837074.470137, "relative_end": 1749837074.470137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.470594, "relative_start": 3.304231882095337, "end": 1749837074.470594, "relative_end": 1749837074.470594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.470948, "relative_start": 3.3045859336853027, "end": 1749837074.470948, "relative_end": 1749837074.470948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.471274, "relative_start": 3.3049118518829346, "end": 1749837074.471274, "relative_end": 1749837074.471274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.471591, "relative_start": 3.3052289485931396, "end": 1749837074.471591, "relative_end": 1749837074.471591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.471863, "relative_start": 3.3055009841918945, "end": 1749837074.471863, "relative_end": 1749837074.471863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.472283, "relative_start": 3.3059208393096924, "end": 1749837074.472283, "relative_end": 1749837074.472283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.472486, "relative_start": 3.306123971939087, "end": 1749837074.472486, "relative_end": 1749837074.472486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.472711, "relative_start": 3.306349039077759, "end": 1749837074.472711, "relative_end": 1749837074.472711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.472982, "relative_start": 3.306619882583618, "end": 1749837074.472982, "relative_end": 1749837074.472982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.473282, "relative_start": 3.306920051574707, "end": 1749837074.473282, "relative_end": 1749837074.473282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.473704, "relative_start": 3.307342052459717, "end": 1749837074.473704, "relative_end": 1749837074.473704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.473907, "relative_start": 3.3075449466705322, "end": 1749837074.473907, "relative_end": 1749837074.473907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.474133, "relative_start": 3.3077709674835205, "end": 1749837074.474133, "relative_end": 1749837074.474133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.4744, "relative_start": 3.3080379962921143, "end": 1749837074.4744, "relative_end": 1749837074.4744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.474698, "relative_start": 3.308336019515991, "end": 1749837074.474698, "relative_end": 1749837074.474698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.475098, "relative_start": 3.3087358474731445, "end": 1749837074.475098, "relative_end": 1749837074.475098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.475299, "relative_start": 3.308936834335327, "end": 1749837074.475299, "relative_end": 1749837074.475299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.475526, "relative_start": 3.309164047241211, "end": 1749837074.475526, "relative_end": 1749837074.475526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.475796, "relative_start": 3.309433937072754, "end": 1749837074.475796, "relative_end": 1749837074.475796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.476121, "relative_start": 3.3097589015960693, "end": 1749837074.476121, "relative_end": 1749837074.476121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.476428, "relative_start": 3.310065984725952, "end": 1749837074.476428, "relative_end": 1749837074.476428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.476748, "relative_start": 3.3103859424591064, "end": 1749837074.476748, "relative_end": 1749837074.476748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.477149, "relative_start": 3.3107869625091553, "end": 1749837074.477149, "relative_end": 1749837074.477149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.477377, "relative_start": 3.3110148906707764, "end": 1749837074.477377, "relative_end": 1749837074.477377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.477602, "relative_start": 3.3112399578094482, "end": 1749837074.477602, "relative_end": 1749837074.477602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.47787, "relative_start": 3.3115079402923584, "end": 1749837074.47787, "relative_end": 1749837074.47787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.478196, "relative_start": 3.3118338584899902, "end": 1749837074.478196, "relative_end": 1749837074.478196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.478503, "relative_start": 3.312140941619873, "end": 1749837074.478503, "relative_end": 1749837074.478503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.478822, "relative_start": 3.312459945678711, "end": 1749837074.478822, "relative_end": 1749837074.478822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.479222, "relative_start": 3.3128600120544434, "end": 1749837074.479222, "relative_end": 1749837074.479222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.479425, "relative_start": 3.313062906265259, "end": 1749837074.479425, "relative_end": 1749837074.479425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.47988, "relative_start": 3.3135180473327637, "end": 1749837074.47988, "relative_end": 1749837074.47988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.480266, "relative_start": 3.313904047012329, "end": 1749837074.480266, "relative_end": 1749837074.480266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.480652, "relative_start": 3.3142900466918945, "end": 1749837074.480652, "relative_end": 1749837074.480652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.481014, "relative_start": 3.3146519660949707, "end": 1749837074.481014, "relative_end": 1749837074.481014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.481374, "relative_start": 3.315011978149414, "end": 1749837074.481374, "relative_end": 1749837074.481374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.481824, "relative_start": 3.3154618740081787, "end": 1749837074.481824, "relative_end": 1749837074.481824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.48206, "relative_start": 3.3156979084014893, "end": 1749837074.48206, "relative_end": 1749837074.48206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.482315, "relative_start": 3.315953016281128, "end": 1749837074.482315, "relative_end": 1749837074.482315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.482609, "relative_start": 3.31624698638916, "end": 1749837074.482609, "relative_end": 1749837074.482609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.482956, "relative_start": 3.316593885421753, "end": 1749837074.482956, "relative_end": 1749837074.482956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.483274, "relative_start": 3.3169119358062744, "end": 1749837074.483274, "relative_end": 1749837074.483274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.483619, "relative_start": 3.3172569274902344, "end": 1749837074.483619, "relative_end": 1749837074.483619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.484021, "relative_start": 3.3176589012145996, "end": 1749837074.484021, "relative_end": 1749837074.484021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.484233, "relative_start": 3.317870855331421, "end": 1749837074.484233, "relative_end": 1749837074.484233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.484489, "relative_start": 3.318126916885376, "end": 1749837074.484489, "relative_end": 1749837074.484489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.484764, "relative_start": 3.318402051925659, "end": 1749837074.484764, "relative_end": 1749837074.484764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.485118, "relative_start": 3.318755865097046, "end": 1749837074.485118, "relative_end": 1749837074.485118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.485434, "relative_start": 3.3190720081329346, "end": 1749837074.485434, "relative_end": 1749837074.485434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.485859, "relative_start": 3.3194968700408936, "end": 1749837074.485859, "relative_end": 1749837074.485859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.4863, "relative_start": 3.3199379444122314, "end": 1749837074.4863, "relative_end": 1749837074.4863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.486517, "relative_start": 3.320154905319214, "end": 1749837074.486517, "relative_end": 1749837074.486517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.486756, "relative_start": 3.3203940391540527, "end": 1749837074.486756, "relative_end": 1749837074.486756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.48717, "relative_start": 3.320807933807373, "end": 1749837074.48717, "relative_end": 1749837074.48717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.581651, "relative_start": 3.4152889251708984, "end": 1749837074.581651, "relative_end": 1749837074.581651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.683939, "relative_start": 3.5175769329071045, "end": 1749837074.683939, "relative_end": 1749837074.683939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.684305, "relative_start": 3.5179429054260254, "end": 1749837074.684305, "relative_end": 1749837074.684305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.685104, "relative_start": 3.5187418460845947, "end": 1749837074.685104, "relative_end": 1749837074.685104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.685647, "relative_start": 3.519284963607788, "end": 1749837074.685647, "relative_end": 1749837074.685647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.685919, "relative_start": 3.519556999206543, "end": 1749837074.685919, "relative_end": 1749837074.685919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.686222, "relative_start": 3.519860029220581, "end": 1749837074.686222, "relative_end": 1749837074.686222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.68664, "relative_start": 3.520277976989746, "end": 1749837074.68664, "relative_end": 1749837074.68664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.687303, "relative_start": 3.5209410190582275, "end": 1749837074.687303, "relative_end": 1749837074.687303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.687823, "relative_start": 3.521461009979248, "end": 1749837074.687823, "relative_end": 1749837074.687823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.688198, "relative_start": 3.521836042404175, "end": 1749837074.688198, "relative_end": 1749837074.688198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.688634, "relative_start": 3.5222718715667725, "end": 1749837074.688634, "relative_end": 1749837074.688634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.68885, "relative_start": 3.5224878787994385, "end": 1749837074.68885, "relative_end": 1749837074.68885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.6891, "relative_start": 3.522737979888916, "end": 1749837074.6891, "relative_end": 1749837074.6891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.689429, "relative_start": 3.523066997528076, "end": 1749837074.689429, "relative_end": 1749837074.689429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.689828, "relative_start": 3.523465871810913, "end": 1749837074.689828, "relative_end": 1749837074.689828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.690167, "relative_start": 3.5238049030303955, "end": 1749837074.690167, "relative_end": 1749837074.690167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.69065, "relative_start": 3.5242879390716553, "end": 1749837074.69065, "relative_end": 1749837074.69065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.690923, "relative_start": 3.5245609283447266, "end": 1749837074.690923, "relative_end": 1749837074.690923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.691339, "relative_start": 3.524976968765259, "end": 1749837074.691339, "relative_end": 1749837074.691339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.691556, "relative_start": 3.525193929672241, "end": 1749837074.691556, "relative_end": 1749837074.691556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.691815, "relative_start": 3.5254528522491455, "end": 1749837074.691815, "relative_end": 1749837074.691815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.692099, "relative_start": 3.5257370471954346, "end": 1749837074.692099, "relative_end": 1749837074.692099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.692407, "relative_start": 3.5260448455810547, "end": 1749837074.692407, "relative_end": 1749837074.692407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.692831, "relative_start": 3.5264689922332764, "end": 1749837074.692831, "relative_end": 1749837074.692831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.693043, "relative_start": 3.5266809463500977, "end": 1749837074.693043, "relative_end": 1749837074.693043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.693279, "relative_start": 3.526916980743408, "end": 1749837074.693279, "relative_end": 1749837074.693279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.693566, "relative_start": 3.5272040367126465, "end": 1749837074.693566, "relative_end": 1749837074.693566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.693882, "relative_start": 3.527519941329956, "end": 1749837074.693882, "relative_end": 1749837074.693882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.694282, "relative_start": 3.5279200077056885, "end": 1749837074.694282, "relative_end": 1749837074.694282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.694493, "relative_start": 3.5281310081481934, "end": 1749837074.694493, "relative_end": 1749837074.694493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.694748, "relative_start": 3.528385877609253, "end": 1749837074.694748, "relative_end": 1749837074.694748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.695027, "relative_start": 3.528665065765381, "end": 1749837074.695027, "relative_end": 1749837074.695027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.695355, "relative_start": 3.5289928913116455, "end": 1749837074.695355, "relative_end": 1749837074.695355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.695688, "relative_start": 3.5293259620666504, "end": 1749837074.695688, "relative_end": 1749837074.695688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.696014, "relative_start": 3.5296518802642822, "end": 1749837074.696014, "relative_end": 1749837074.696014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.696417, "relative_start": 3.530055046081543, "end": 1749837074.696417, "relative_end": 1749837074.696417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.69665, "relative_start": 3.530287981033325, "end": 1749837074.69665, "relative_end": 1749837074.69665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.696887, "relative_start": 3.530524969100952, "end": 1749837074.696887, "relative_end": 1749837074.696887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.697161, "relative_start": 3.53079891204834, "end": 1749837074.697161, "relative_end": 1749837074.697161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.69749, "relative_start": 3.5311279296875, "end": 1749837074.69749, "relative_end": 1749837074.69749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.697823, "relative_start": 3.531461000442505, "end": 1749837074.697823, "relative_end": 1749837074.697823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.698148, "relative_start": 3.5317859649658203, "end": 1749837074.698148, "relative_end": 1749837074.698148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.698549, "relative_start": 3.532186985015869, "end": 1749837074.698549, "relative_end": 1749837074.698549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.698783, "relative_start": 3.5324208736419678, "end": 1749837074.698783, "relative_end": 1749837074.698783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.699019, "relative_start": 3.5326569080352783, "end": 1749837074.699019, "relative_end": 1749837074.699019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.699295, "relative_start": 3.532932996749878, "end": 1749837074.699295, "relative_end": 1749837074.699295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.699646, "relative_start": 3.5332839488983154, "end": 1749837074.699646, "relative_end": 1749837074.699646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.699967, "relative_start": 3.533604860305786, "end": 1749837074.699967, "relative_end": 1749837074.699967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.70029, "relative_start": 3.5339279174804688, "end": 1749837074.70029, "relative_end": 1749837074.70029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.700735, "relative_start": 3.5343730449676514, "end": 1749837074.700735, "relative_end": 1749837074.700735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.700949, "relative_start": 3.5345869064331055, "end": 1749837074.700949, "relative_end": 1749837074.700949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.701186, "relative_start": 3.5348238945007324, "end": 1749837074.701186, "relative_end": 1749837074.701186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.701461, "relative_start": 3.5350990295410156, "end": 1749837074.701461, "relative_end": 1749837074.701461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.701812, "relative_start": 3.535449981689453, "end": 1749837074.701812, "relative_end": 1749837074.701812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.702174, "relative_start": 3.5358119010925293, "end": 1749837074.702174, "relative_end": 1749837074.702174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.702543, "relative_start": 3.5361809730529785, "end": 1749837074.702543, "relative_end": 1749837074.702543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.703274, "relative_start": 3.536911964416504, "end": 1749837074.703274, "relative_end": 1749837074.703274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.70363, "relative_start": 3.5372679233551025, "end": 1749837074.70363, "relative_end": 1749837074.70363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.704049, "relative_start": 3.537687063217163, "end": 1749837074.704049, "relative_end": 1749837074.704049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.704604, "relative_start": 3.5382418632507324, "end": 1749837074.704604, "relative_end": 1749837074.704604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.705008, "relative_start": 3.5386459827423096, "end": 1749837074.705008, "relative_end": 1749837074.705008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.705369, "relative_start": 3.5390069484710693, "end": 1749837074.705369, "relative_end": 1749837074.705369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.70576, "relative_start": 3.539397954940796, "end": 1749837074.70576, "relative_end": 1749837074.70576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.706193, "relative_start": 3.5398309230804443, "end": 1749837074.706193, "relative_end": 1749837074.706193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.706425, "relative_start": 3.54006290435791, "end": 1749837074.706425, "relative_end": 1749837074.706425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.706682, "relative_start": 3.5403199195861816, "end": 1749837074.706682, "relative_end": 1749837074.706682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.706995, "relative_start": 3.540632963180542, "end": 1749837074.706995, "relative_end": 1749837074.706995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.707318, "relative_start": 3.5409560203552246, "end": 1749837074.707318, "relative_end": 1749837074.707318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.70776, "relative_start": 3.541398048400879, "end": 1749837074.70776, "relative_end": 1749837074.70776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.707983, "relative_start": 3.541620969772339, "end": 1749837074.707983, "relative_end": 1749837074.707983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.708233, "relative_start": 3.5418710708618164, "end": 1749837074.708233, "relative_end": 1749837074.708233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.708534, "relative_start": 3.5421719551086426, "end": 1749837074.708534, "relative_end": 1749837074.708534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.708873, "relative_start": 3.542510986328125, "end": 1749837074.708873, "relative_end": 1749837074.708873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.70929, "relative_start": 3.5429279804229736, "end": 1749837074.70929, "relative_end": 1749837074.70929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.709514, "relative_start": 3.54315185546875, "end": 1749837074.709514, "relative_end": 1749837074.709514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.709761, "relative_start": 3.543398857116699, "end": 1749837074.709761, "relative_end": 1749837074.709761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.710057, "relative_start": 3.5436949729919434, "end": 1749837074.710057, "relative_end": 1749837074.710057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.710406, "relative_start": 3.544044017791748, "end": 1749837074.710406, "relative_end": 1749837074.710406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.71076, "relative_start": 3.544398069381714, "end": 1749837074.71076, "relative_end": 1749837074.71076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.711107, "relative_start": 3.5447449684143066, "end": 1749837074.711107, "relative_end": 1749837074.711107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.711525, "relative_start": 3.5451629161834717, "end": 1749837074.711525, "relative_end": 1749837074.711525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.711747, "relative_start": 3.5453848838806152, "end": 1749837074.711747, "relative_end": 1749837074.711747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.711994, "relative_start": 3.5456318855285645, "end": 1749837074.711994, "relative_end": 1749837074.711994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.712284, "relative_start": 3.545922040939331, "end": 1749837074.712284, "relative_end": 1749837074.712284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.712628, "relative_start": 3.5462658405303955, "end": 1749837074.712628, "relative_end": 1749837074.712628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.712976, "relative_start": 3.546613931655884, "end": 1749837074.712976, "relative_end": 1749837074.712976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.713317, "relative_start": 3.546954870223999, "end": 1749837074.713317, "relative_end": 1749837074.713317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.713756, "relative_start": 3.547394037246704, "end": 1749837074.713756, "relative_end": 1749837074.713756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.713978, "relative_start": 3.5476160049438477, "end": 1749837074.713978, "relative_end": 1749837074.713978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.714224, "relative_start": 3.5478620529174805, "end": 1749837074.714224, "relative_end": 1749837074.714224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.714511, "relative_start": 3.5481488704681396, "end": 1749837074.714511, "relative_end": 1749837074.714511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.714855, "relative_start": 3.548492908477783, "end": 1749837074.714855, "relative_end": 1749837074.714855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.715184, "relative_start": 3.5488219261169434, "end": 1749837074.715184, "relative_end": 1749837074.715184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.715527, "relative_start": 3.5491650104522705, "end": 1749837074.715527, "relative_end": 1749837074.715527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.715946, "relative_start": 3.549583911895752, "end": 1749837074.715946, "relative_end": 1749837074.715946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.716165, "relative_start": 3.5498030185699463, "end": 1749837074.716165, "relative_end": 1749837074.716165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.71641, "relative_start": 3.5500478744506836, "end": 1749837074.71641, "relative_end": 1749837074.71641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.716705, "relative_start": 3.5503430366516113, "end": 1749837074.716705, "relative_end": 1749837074.716705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.717065, "relative_start": 3.5507030487060547, "end": 1749837074.717065, "relative_end": 1749837074.717065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.71739, "relative_start": 3.55102801322937, "end": 1749837074.71739, "relative_end": 1749837074.71739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.717714, "relative_start": 3.551352024078369, "end": 1749837074.717714, "relative_end": 1749837074.717714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.718006, "relative_start": 3.5516438484191895, "end": 1749837074.718006, "relative_end": 1749837074.718006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.718428, "relative_start": 3.552065849304199, "end": 1749837074.718428, "relative_end": 1749837074.718428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.718649, "relative_start": 3.5522868633270264, "end": 1749837074.718649, "relative_end": 1749837074.718649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.718996, "relative_start": 3.5526340007781982, "end": 1749837074.718996, "relative_end": 1749837074.718996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.719305, "relative_start": 3.552942991256714, "end": 1749837074.719305, "relative_end": 1749837074.719305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.719634, "relative_start": 3.553272008895874, "end": 1749837074.719634, "relative_end": 1749837074.719634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.720131, "relative_start": 3.5537688732147217, "end": 1749837074.720131, "relative_end": 1749837074.720131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.720366, "relative_start": 3.554003953933716, "end": 1749837074.720366, "relative_end": 1749837074.720366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.720609, "relative_start": 3.5542469024658203, "end": 1749837074.720609, "relative_end": 1749837074.720609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.720906, "relative_start": 3.554543972015381, "end": 1749837074.720906, "relative_end": 1749837074.720906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.72122, "relative_start": 3.5548579692840576, "end": 1749837074.72122, "relative_end": 1749837074.72122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.721634, "relative_start": 3.555271863937378, "end": 1749837074.721634, "relative_end": 1749837074.721634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.721849, "relative_start": 3.5554869174957275, "end": 1749837074.721849, "relative_end": 1749837074.721849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.722079, "relative_start": 3.5557169914245605, "end": 1749837074.722079, "relative_end": 1749837074.722079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.722358, "relative_start": 3.5559959411621094, "end": 1749837074.722358, "relative_end": 1749837074.722358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.722688, "relative_start": 3.556325912475586, "end": 1749837074.722688, "relative_end": 1749837074.722688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.723007, "relative_start": 3.556644916534424, "end": 1749837074.723007, "relative_end": 1749837074.723007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.723342, "relative_start": 3.5569798946380615, "end": 1749837074.723342, "relative_end": 1749837074.723342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.723861, "relative_start": 3.5574989318847656, "end": 1749837074.723861, "relative_end": 1749837074.723861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.724064, "relative_start": 3.55770206451416, "end": 1749837074.724064, "relative_end": 1749837074.724064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.724292, "relative_start": 3.5579299926757812, "end": 1749837074.724292, "relative_end": 1749837074.724292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.724564, "relative_start": 3.558202028274536, "end": 1749837074.724564, "relative_end": 1749837074.724564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.72489, "relative_start": 3.558527946472168, "end": 1749837074.72489, "relative_end": 1749837074.72489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.725198, "relative_start": 3.558835983276367, "end": 1749837074.725198, "relative_end": 1749837074.725198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.725525, "relative_start": 3.5591628551483154, "end": 1749837074.725525, "relative_end": 1749837074.725525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.725927, "relative_start": 3.5595650672912598, "end": 1749837074.725927, "relative_end": 1749837074.725927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.726126, "relative_start": 3.5597639083862305, "end": 1749837074.726126, "relative_end": 1749837074.726126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.726354, "relative_start": 3.5599918365478516, "end": 1749837074.726354, "relative_end": 1749837074.726354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.726624, "relative_start": 3.5602619647979736, "end": 1749837074.726624, "relative_end": 1749837074.726624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.726944, "relative_start": 3.560581922531128, "end": 1749837074.726944, "relative_end": 1749837074.726944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.727249, "relative_start": 3.560886859893799, "end": 1749837074.727249, "relative_end": 1749837074.727249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.72757, "relative_start": 3.5612080097198486, "end": 1749837074.72757, "relative_end": 1749837074.72757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.727971, "relative_start": 3.5616090297698975, "end": 1749837074.727971, "relative_end": 1749837074.727971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.728171, "relative_start": 3.5618090629577637, "end": 1749837074.728171, "relative_end": 1749837074.728171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.728397, "relative_start": 3.562034845352173, "end": 1749837074.728397, "relative_end": 1749837074.728397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.728666, "relative_start": 3.5623040199279785, "end": 1749837074.728666, "relative_end": 1749837074.728666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.728988, "relative_start": 3.5626258850097656, "end": 1749837074.728988, "relative_end": 1749837074.728988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.729293, "relative_start": 3.5629310607910156, "end": 1749837074.729293, "relative_end": 1749837074.729293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.729614, "relative_start": 3.5632519721984863, "end": 1749837074.729614, "relative_end": 1749837074.729614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.730012, "relative_start": 3.563649892807007, "end": 1749837074.730012, "relative_end": 1749837074.730012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.730214, "relative_start": 3.563852071762085, "end": 1749837074.730214, "relative_end": 1749837074.730214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.730459, "relative_start": 3.5640969276428223, "end": 1749837074.730459, "relative_end": 1749837074.730459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.730836, "relative_start": 3.564473867416382, "end": 1749837074.730836, "relative_end": 1749837074.730836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.731567, "relative_start": 3.5652048587799072, "end": 1749837074.731567, "relative_end": 1749837074.731567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.732252, "relative_start": 3.565889835357666, "end": 1749837074.732252, "relative_end": 1749837074.732252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.73297, "relative_start": 3.56660795211792, "end": 1749837074.73297, "relative_end": 1749837074.73297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.733868, "relative_start": 3.5675058364868164, "end": 1749837074.733868, "relative_end": 1749837074.733868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.734321, "relative_start": 3.5679590702056885, "end": 1749837074.734321, "relative_end": 1749837074.734321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.734946, "relative_start": 3.5685839653015137, "end": 1749837074.734946, "relative_end": 1749837074.734946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1749837074.73579, "relative_start": 3.569427967071533, "end": 1749837074.73579, "relative_end": 1749837074.73579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.736818, "relative_start": 3.570456027984619, "end": 1749837074.736818, "relative_end": 1749837074.736818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.737579, "relative_start": 3.5712170600891113, "end": 1749837074.737579, "relative_end": 1749837074.737579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1749837074.738365, "relative_start": 3.57200288772583, "end": 1749837074.738365, "relative_end": 1749837074.738365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.738998, "relative_start": 3.5726358890533447, "end": 1749837074.738998, "relative_end": 1749837074.738998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.740012, "relative_start": 3.5736498832702637, "end": 1749837074.740012, "relative_end": 1749837074.740012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.740516, "relative_start": 3.5741539001464844, "end": 1749837074.740516, "relative_end": 1749837074.740516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.741064, "relative_start": 3.574702024459839, "end": 1749837074.741064, "relative_end": 1749837074.741064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.741711, "relative_start": 3.5753488540649414, "end": 1749837074.741711, "relative_end": 1749837074.741711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.742409, "relative_start": 3.576046943664551, "end": 1749837074.742409, "relative_end": 1749837074.742409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.743333, "relative_start": 3.5769710540771484, "end": 1749837074.743333, "relative_end": 1749837074.743333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.743795, "relative_start": 3.577432870864868, "end": 1749837074.743795, "relative_end": 1749837074.743795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.744331, "relative_start": 3.5779688358306885, "end": 1749837074.744331, "relative_end": 1749837074.744331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.744959, "relative_start": 3.578597068786621, "end": 1749837074.744959, "relative_end": 1749837074.744959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.745629, "relative_start": 3.5792670249938965, "end": 1749837074.745629, "relative_end": 1749837074.745629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.746545, "relative_start": 3.5801830291748047, "end": 1749837074.746545, "relative_end": 1749837074.746545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.747014, "relative_start": 3.5806519985198975, "end": 1749837074.747014, "relative_end": 1749837074.747014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.747531, "relative_start": 3.5811688899993896, "end": 1749837074.747531, "relative_end": 1749837074.747531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.74816, "relative_start": 3.5817978382110596, "end": 1749837074.74816, "relative_end": 1749837074.74816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.748908, "relative_start": 3.5825459957122803, "end": 1749837074.748908, "relative_end": 1749837074.748908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.749622, "relative_start": 3.5832600593566895, "end": 1749837074.749622, "relative_end": 1749837074.749622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.750819, "relative_start": 3.5844569206237793, "end": 1749837074.750819, "relative_end": 1749837074.750819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.752397, "relative_start": 3.5860350131988525, "end": 1749837074.752397, "relative_end": 1749837074.752397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.753539, "relative_start": 3.587177038192749, "end": 1749837074.753539, "relative_end": 1749837074.753539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.75408, "relative_start": 3.5877180099487305, "end": 1749837074.75408, "relative_end": 1749837074.75408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.75468, "relative_start": 3.58831787109375, "end": 1749837074.75468, "relative_end": 1749837074.75468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.755501, "relative_start": 3.589138984680176, "end": 1749837074.755501, "relative_end": 1749837074.755501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.756296, "relative_start": 3.5899338722229004, "end": 1749837074.756296, "relative_end": 1749837074.756296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.757033, "relative_start": 3.5906710624694824, "end": 1749837074.757033, "relative_end": 1749837074.757033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.757709, "relative_start": 3.5913469791412354, "end": 1749837074.757709, "relative_end": 1749837074.757709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.759263, "relative_start": 3.5929009914398193, "end": 1749837074.759263, "relative_end": 1749837074.759263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.760208, "relative_start": 3.593845844268799, "end": 1749837074.760208, "relative_end": 1749837074.760208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.76071, "relative_start": 3.5943479537963867, "end": 1749837074.76071, "relative_end": 1749837074.76071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.761042, "relative_start": 3.594680070877075, "end": 1749837074.761042, "relative_end": 1749837074.761042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.761467, "relative_start": 3.595104932785034, "end": 1749837074.761467, "relative_end": 1749837074.761467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.761934, "relative_start": 3.595571994781494, "end": 1749837074.761934, "relative_end": 1749837074.761934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.76237, "relative_start": 3.596008062362671, "end": 1749837074.76237, "relative_end": 1749837074.76237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.762757, "relative_start": 3.5963950157165527, "end": 1749837074.762757, "relative_end": 1749837074.762757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.763672, "relative_start": 3.5973100662231445, "end": 1749837074.763672, "relative_end": 1749837074.763672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.764242, "relative_start": 3.5978798866271973, "end": 1749837074.764242, "relative_end": 1749837074.764242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.764526, "relative_start": 3.5981638431549072, "end": 1749837074.764526, "relative_end": 1749837074.764526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.764847, "relative_start": 3.598484992980957, "end": 1749837074.764847, "relative_end": 1749837074.764847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.76526, "relative_start": 3.598897933959961, "end": 1749837074.76526, "relative_end": 1749837074.76526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.765717, "relative_start": 3.5993549823760986, "end": 1749837074.765717, "relative_end": 1749837074.765717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.766159, "relative_start": 3.599797010421753, "end": 1749837074.766159, "relative_end": 1749837074.766159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.766543, "relative_start": 3.6001808643341064, "end": 1749837074.766543, "relative_end": 1749837074.766543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.767428, "relative_start": 3.6010658740997314, "end": 1749837074.767428, "relative_end": 1749837074.767428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.767999, "relative_start": 3.6016368865966797, "end": 1749837074.767999, "relative_end": 1749837074.767999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.768282, "relative_start": 3.6019198894500732, "end": 1749837074.768282, "relative_end": 1749837074.768282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.768598, "relative_start": 3.602236032485962, "end": 1749837074.768598, "relative_end": 1749837074.768598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.769012, "relative_start": 3.6026499271392822, "end": 1749837074.769012, "relative_end": 1749837074.769012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.769487, "relative_start": 3.6031248569488525, "end": 1749837074.769487, "relative_end": 1749837074.769487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.769948, "relative_start": 3.603585958480835, "end": 1749837074.769948, "relative_end": 1749837074.769948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.770338, "relative_start": 3.603976011276245, "end": 1749837074.770338, "relative_end": 1749837074.770338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.771464, "relative_start": 3.605102062225342, "end": 1749837074.771464, "relative_end": 1749837074.771464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.772063, "relative_start": 3.605700969696045, "end": 1749837074.772063, "relative_end": 1749837074.772063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.772358, "relative_start": 3.6059958934783936, "end": 1749837074.772358, "relative_end": 1749837074.772358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.772691, "relative_start": 3.6063289642333984, "end": 1749837074.772691, "relative_end": 1749837074.772691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.773132, "relative_start": 3.6067700386047363, "end": 1749837074.773132, "relative_end": 1749837074.773132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.773598, "relative_start": 3.607235908508301, "end": 1749837074.773598, "relative_end": 1749837074.773598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.774034, "relative_start": 3.6076719760894775, "end": 1749837074.774034, "relative_end": 1749837074.774034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.774424, "relative_start": 3.6080620288848877, "end": 1749837074.774424, "relative_end": 1749837074.774424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.77534, "relative_start": 3.608978033065796, "end": 1749837074.77534, "relative_end": 1749837074.77534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.775915, "relative_start": 3.6095528602600098, "end": 1749837074.775915, "relative_end": 1749837074.775915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.776204, "relative_start": 3.60984206199646, "end": 1749837074.776204, "relative_end": 1749837074.776204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.776527, "relative_start": 3.6101648807525635, "end": 1749837074.776527, "relative_end": 1749837074.776527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": 1749837074.777115, "relative_start": 3.610753059387207, "end": 1749837074.777115, "relative_end": 1749837074.777115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.778104, "relative_start": 3.6117420196533203, "end": 1749837074.778104, "relative_end": 1749837074.778104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.778563, "relative_start": 3.612200975418091, "end": 1749837074.778563, "relative_end": 1749837074.778563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": 1749837074.779155, "relative_start": 3.61279296875, "end": 1749837074.779155, "relative_end": 1749837074.779155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": 1749837074.780129, "relative_start": 3.61376690864563, "end": 1749837074.780129, "relative_end": 1749837074.780129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1749837074.781812, "relative_start": 3.615449905395508, "end": 1749837074.781812, "relative_end": 1749837074.781812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1749837074.7827, "relative_start": 3.616338014602661, "end": 1749837074.7827, "relative_end": 1749837074.7827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1749837074.784352, "relative_start": 3.617990016937256, "end": 1749837074.784352, "relative_end": 1749837074.784352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.785873, "relative_start": 3.6195108890533447, "end": 1749837074.785873, "relative_end": 1749837074.785873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.786635, "relative_start": 3.6202728748321533, "end": 1749837074.786635, "relative_end": 1749837074.786635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.786933, "relative_start": 3.6205708980560303, "end": 1749837074.786933, "relative_end": 1749837074.786933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.787311, "relative_start": 3.6209490299224854, "end": 1749837074.787311, "relative_end": 1749837074.787311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": 1749837074.788343, "relative_start": 3.621980905532837, "end": 1749837074.788343, "relative_end": 1749837074.788343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.919437, "relative_start": 3.753074884414673, "end": 1749837074.919437, "relative_end": 1749837074.919437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.91994, "relative_start": 3.753577947616577, "end": 1749837074.91994, "relative_end": 1749837074.91994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.9204, "relative_start": 3.754037857055664, "end": 1749837074.9204, "relative_end": 1749837074.9204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.920864, "relative_start": 3.754502058029175, "end": 1749837074.920864, "relative_end": 1749837074.920864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.921088, "relative_start": 3.754725933074951, "end": 1749837074.921088, "relative_end": 1749837074.921088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.921361, "relative_start": 3.7549989223480225, "end": 1749837074.921361, "relative_end": 1749837074.921361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.92173, "relative_start": 3.7553679943084717, "end": 1749837074.92173, "relative_end": 1749837074.92173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.922075, "relative_start": 3.7557129859924316, "end": 1749837074.922075, "relative_end": 1749837074.922075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.922404, "relative_start": 3.756042003631592, "end": 1749837074.922404, "relative_end": 1749837074.922404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.922703, "relative_start": 3.756340980529785, "end": 1749837074.922703, "relative_end": 1749837074.922703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.92351, "relative_start": 3.757148027420044, "end": 1749837074.92351, "relative_end": 1749837074.92351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.924082, "relative_start": 3.7577199935913086, "end": 1749837074.924082, "relative_end": 1749837074.924082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.924314, "relative_start": 3.7579519748687744, "end": 1749837074.924314, "relative_end": 1749837074.924314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.924569, "relative_start": 3.758206844329834, "end": 1749837074.924569, "relative_end": 1749837074.924569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1749837074.924896, "relative_start": 3.7585339546203613, "end": 1749837074.924896, "relative_end": 1749837074.924896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1749837074.92525, "relative_start": 3.758888006210327, "end": 1749837074.92525, "relative_end": 1749837074.92525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1749837074.925585, "relative_start": 3.759222984313965, "end": 1749837074.925585, "relative_end": 1749837074.925585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1749837074.926022, "relative_start": 3.759660005569458, "end": 1749837074.926022, "relative_end": 1749837074.926022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.92688, "relative_start": 3.7605178356170654, "end": 1749837074.92688, "relative_end": 1749837074.92688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.927302, "relative_start": 3.760939836502075, "end": 1749837074.927302, "relative_end": 1749837074.927302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.927518, "relative_start": 3.761155843734741, "end": 1749837074.927518, "relative_end": 1749837074.927518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.92776, "relative_start": 3.7613978385925293, "end": 1749837074.92776, "relative_end": 1749837074.92776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.928067, "relative_start": 3.761704921722412, "end": 1749837074.928067, "relative_end": 1749837074.928067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.928376, "relative_start": 3.7620139122009277, "end": 1749837074.928376, "relative_end": 1749837074.928376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.928775, "relative_start": 3.7624130249023438, "end": 1749837074.928775, "relative_end": 1749837074.928775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.928987, "relative_start": 3.762624979019165, "end": 1749837074.928987, "relative_end": 1749837074.928987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.929225, "relative_start": 3.7628629207611084, "end": 1749837074.929225, "relative_end": 1749837074.929225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.929502, "relative_start": 3.7631399631500244, "end": 1749837074.929502, "relative_end": 1749837074.929502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.929806, "relative_start": 3.763443946838379, "end": 1749837074.929806, "relative_end": 1749837074.929806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.930204, "relative_start": 3.7638418674468994, "end": 1749837074.930204, "relative_end": 1749837074.930204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.930416, "relative_start": 3.7640540599823, "end": 1749837074.930416, "relative_end": 1749837074.930416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.930667, "relative_start": 3.7643048763275146, "end": 1749837074.930667, "relative_end": 1749837074.930667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1749837074.930964, "relative_start": 3.764601945877075, "end": 1749837074.930964, "relative_end": 1749837074.930964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.931266, "relative_start": 3.764904022216797, "end": 1749837074.931266, "relative_end": 1749837074.931266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.931667, "relative_start": 3.7653050422668457, "end": 1749837074.931667, "relative_end": 1749837074.931667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.931891, "relative_start": 3.765528917312622, "end": 1749837074.931891, "relative_end": 1749837074.931891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.932137, "relative_start": 3.765774965286255, "end": 1749837074.932137, "relative_end": 1749837074.932137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1749837074.932421, "relative_start": 3.766058921813965, "end": 1749837074.932421, "relative_end": 1749837074.932421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1749837074.932757, "relative_start": 3.766394853591919, "end": 1749837074.932757, "relative_end": 1749837074.932757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1749837074.933078, "relative_start": 3.7667160034179688, "end": 1749837074.933078, "relative_end": 1749837074.933078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1749837074.933419, "relative_start": 3.767056941986084, "end": 1749837074.933419, "relative_end": 1749837074.933419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1749837074.933818, "relative_start": 3.7674560546875, "end": 1749837074.933818, "relative_end": 1749837074.933818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1749837074.934054, "relative_start": 3.7676918506622314, "end": 1749837074.934054, "relative_end": 1749837074.934054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1749837074.934289, "relative_start": 3.7679269313812256, "end": 1749837074.934289, "relative_end": 1749837074.934289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.alert", "start": 1749837074.934701, "relative_start": 3.768338918685913, "end": 1749837074.934701, "relative_end": 1749837074.934701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.126607, "relative_start": 3.96024489402771, "end": **********.126607, "relative_end": **********.126607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::71e371662f90e963b363342e7e4f0ed5", "start": **********.128293, "relative_start": 3.961930990219116, "end": **********.128293, "relative_end": **********.128293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.142824, "relative_start": 3.976461887359619, "end": **********.142824, "relative_end": **********.142824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.143503, "relative_start": 3.9771409034729004, "end": **********.143503, "relative_end": **********.143503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.143872, "relative_start": 3.9775099754333496, "end": **********.143872, "relative_end": **********.143872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.144123, "relative_start": 3.9777610301971436, "end": **********.144123, "relative_end": **********.144123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.144393, "relative_start": 3.9780309200286865, "end": **********.144393, "relative_end": **********.144393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.144743, "relative_start": 3.9783809185028076, "end": **********.144743, "relative_end": **********.144743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.145063, "relative_start": 3.978700876235962, "end": **********.145063, "relative_end": **********.145063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.145467, "relative_start": 3.979104995727539, "end": **********.145467, "relative_end": **********.145467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.145684, "relative_start": 3.9793219566345215, "end": **********.145684, "relative_end": **********.145684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.145923, "relative_start": 3.9795608520507812, "end": **********.145923, "relative_end": **********.145923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.146207, "relative_start": 3.9798450469970703, "end": **********.146207, "relative_end": **********.146207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.146514, "relative_start": 3.980151891708374, "end": **********.146514, "relative_end": **********.146514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.146915, "relative_start": 3.980552911758423, "end": **********.146915, "relative_end": **********.146915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.147137, "relative_start": 3.9807748794555664, "end": **********.147137, "relative_end": **********.147137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.147386, "relative_start": 3.9810240268707275, "end": **********.147386, "relative_end": **********.147386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.147667, "relative_start": 3.981304883956909, "end": **********.147667, "relative_end": **********.147667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.147973, "relative_start": 3.9816110134124756, "end": **********.147973, "relative_end": **********.147973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.148387, "relative_start": 3.982024908065796, "end": **********.148387, "relative_end": **********.148387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.148603, "relative_start": 3.982240915298462, "end": **********.148603, "relative_end": **********.148603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.148843, "relative_start": 3.982481002807617, "end": **********.148843, "relative_end": **********.148843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.149128, "relative_start": 3.9827659130096436, "end": **********.149128, "relative_end": **********.149128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.149436, "relative_start": 3.9830739498138428, "end": **********.149436, "relative_end": **********.149436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.149839, "relative_start": 3.9834768772125244, "end": **********.149839, "relative_end": **********.149839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.150111, "relative_start": 3.9837489128112793, "end": **********.150111, "relative_end": **********.150111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.150368, "relative_start": 3.984005928039551, "end": **********.150368, "relative_end": **********.150368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.150665, "relative_start": 3.9843029975891113, "end": **********.150665, "relative_end": **********.150665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.150989, "relative_start": 3.9846270084381104, "end": **********.150989, "relative_end": **********.150989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.151422, "relative_start": 3.985059976577759, "end": **********.151422, "relative_end": **********.151422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.151653, "relative_start": 3.985291004180908, "end": **********.151653, "relative_end": **********.151653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.15191, "relative_start": 3.9855480194091797, "end": **********.15191, "relative_end": **********.15191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.152212, "relative_start": 3.9858498573303223, "end": **********.152212, "relative_end": **********.152212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.152553, "relative_start": 3.9861910343170166, "end": **********.152553, "relative_end": **********.152553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.152952, "relative_start": 3.9865899085998535, "end": **********.152952, "relative_end": **********.152952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.153254, "relative_start": 3.986891984939575, "end": **********.153254, "relative_end": **********.153254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.153509, "relative_start": 3.9871468544006348, "end": **********.153509, "relative_end": **********.153509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.153813, "relative_start": 3.9874508380889893, "end": **********.153813, "relative_end": **********.153813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.154179, "relative_start": 3.9878170490264893, "end": **********.154179, "relative_end": **********.154179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.154517, "relative_start": 3.988154888153076, "end": **********.154517, "relative_end": **********.154517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.154873, "relative_start": 3.988510847091675, "end": **********.154873, "relative_end": **********.154873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.155304, "relative_start": 3.9889419078826904, "end": **********.155304, "relative_end": **********.155304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.15554, "relative_start": 3.989177942276001, "end": **********.15554, "relative_end": **********.15554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.155789, "relative_start": 3.989426851272583, "end": **********.155789, "relative_end": **********.155789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.156105, "relative_start": 3.9897429943084717, "end": **********.156105, "relative_end": **********.156105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.156492, "relative_start": 3.9901299476623535, "end": **********.156492, "relative_end": **********.156492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.156897, "relative_start": 3.990535020828247, "end": **********.156897, "relative_end": **********.156897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.157371, "relative_start": 3.991008996963501, "end": **********.157371, "relative_end": **********.157371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.157788, "relative_start": 3.9914259910583496, "end": **********.157788, "relative_end": **********.157788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.158, "relative_start": 3.991637945175171, "end": **********.158, "relative_end": **********.158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.158237, "relative_start": 3.991874933242798, "end": **********.158237, "relative_end": **********.158237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.158551, "relative_start": 3.9921889305114746, "end": **********.158551, "relative_end": **********.158551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.158863, "relative_start": 3.9925010204315186, "end": **********.158863, "relative_end": **********.158863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.159361, "relative_start": 3.9929988384246826, "end": **********.159361, "relative_end": **********.159361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.159585, "relative_start": 3.993222951889038, "end": **********.159585, "relative_end": **********.159585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.159833, "relative_start": 3.9934709072113037, "end": **********.159833, "relative_end": **********.159833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.160121, "relative_start": 3.9937589168548584, "end": **********.160121, "relative_end": **********.160121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.160442, "relative_start": 3.994080066680908, "end": **********.160442, "relative_end": **********.160442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.160863, "relative_start": 3.9945008754730225, "end": **********.160863, "relative_end": **********.160863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.161084, "relative_start": 3.9947218894958496, "end": **********.161084, "relative_end": **********.161084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.16135, "relative_start": 3.994987964630127, "end": **********.16135, "relative_end": **********.16135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.161638, "relative_start": 3.9952759742736816, "end": **********.161638, "relative_end": **********.161638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.161955, "relative_start": 3.9955930709838867, "end": **********.161955, "relative_end": **********.161955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.162392, "relative_start": 3.996029853820801, "end": **********.162392, "relative_end": **********.162392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.162615, "relative_start": 3.99625301361084, "end": **********.162615, "relative_end": **********.162615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.162859, "relative_start": 3.9964969158172607, "end": **********.162859, "relative_end": **********.162859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.163146, "relative_start": 3.996783971786499, "end": **********.163146, "relative_end": **********.163146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.163464, "relative_start": 3.9971020221710205, "end": **********.163464, "relative_end": **********.163464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.163878, "relative_start": 3.997515916824341, "end": **********.163878, "relative_end": **********.163878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.164099, "relative_start": 3.997736930847168, "end": **********.164099, "relative_end": **********.164099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.164364, "relative_start": 3.998002052307129, "end": **********.164364, "relative_end": **********.164364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": **********.164682, "relative_start": 3.9983198642730713, "end": **********.164682, "relative_end": **********.164682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": **********.165036, "relative_start": 3.998673915863037, "end": **********.165036, "relative_end": **********.165036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": **********.165393, "relative_start": 3.9990310668945312, "end": **********.165393, "relative_end": **********.165393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.165704, "relative_start": 3.9993419647216797, "end": **********.165704, "relative_end": **********.165704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.166489, "relative_start": 4.000126838684082, "end": **********.166489, "relative_end": **********.166489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.166919, "relative_start": 4.000556945800781, "end": **********.166919, "relative_end": **********.166919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.167142, "relative_start": 4.000779867172241, "end": **********.167142, "relative_end": **********.167142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.167393, "relative_start": 4.001030921936035, "end": **********.167393, "relative_end": **********.167393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.167707, "relative_start": 4.001344919204712, "end": **********.167707, "relative_end": **********.167707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.16803, "relative_start": 4.0016679763793945, "end": **********.16803, "relative_end": **********.16803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.168469, "relative_start": 4.0021069049835205, "end": **********.168469, "relative_end": **********.168469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.168693, "relative_start": 4.002331018447876, "end": **********.168693, "relative_end": **********.168693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.168954, "relative_start": 4.002591848373413, "end": **********.168954, "relative_end": **********.168954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.169273, "relative_start": 4.002910852432251, "end": **********.169273, "relative_end": **********.169273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.169657, "relative_start": 4.003294944763184, "end": **********.169657, "relative_end": **********.169657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.170088, "relative_start": 4.003726005554199, "end": **********.170088, "relative_end": **********.170088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.170314, "relative_start": 4.0039520263671875, "end": **********.170314, "relative_end": **********.170314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.170562, "relative_start": 4.004199981689453, "end": **********.170562, "relative_end": **********.170562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.170887, "relative_start": 4.0045249462127686, "end": **********.170887, "relative_end": **********.170887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.171205, "relative_start": 4.00484299659729, "end": **********.171205, "relative_end": **********.171205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.171632, "relative_start": 4.005270004272461, "end": **********.171632, "relative_end": **********.171632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.171913, "relative_start": 4.005550861358643, "end": **********.171913, "relative_end": **********.171913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.172167, "relative_start": 4.005805015563965, "end": **********.172167, "relative_end": **********.172167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.172466, "relative_start": 4.006103992462158, "end": **********.172466, "relative_end": **********.172466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.172828, "relative_start": 4.006465911865234, "end": **********.172828, "relative_end": **********.172828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.173162, "relative_start": 4.006799936294556, "end": **********.173162, "relative_end": **********.173162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.173531, "relative_start": 4.007169008255005, "end": **********.173531, "relative_end": **********.173531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": **********.173853, "relative_start": 4.007490873336792, "end": **********.173853, "relative_end": **********.173853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": **********.174678, "relative_start": 4.0083160400390625, "end": **********.174678, "relative_end": **********.174678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.17504, "relative_start": 4.008677959442139, "end": **********.17504, "relative_end": **********.17504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": **********.175691, "relative_start": 4.009328842163086, "end": **********.175691, "relative_end": **********.175691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.175956, "relative_start": 4.009593963623047, "end": **********.175956, "relative_end": **********.175956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.176382, "relative_start": 4.010020017623901, "end": **********.176382, "relative_end": **********.176382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.176606, "relative_start": 4.010243892669678, "end": **********.176606, "relative_end": **********.176606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.176874, "relative_start": 4.010511875152588, "end": **********.176874, "relative_end": **********.176874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.177191, "relative_start": 4.010828971862793, "end": **********.177191, "relative_end": **********.177191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.177536, "relative_start": 4.011173963546753, "end": **********.177536, "relative_end": **********.177536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.177864, "relative_start": 4.011502027511597, "end": **********.177864, "relative_end": **********.177864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.178209, "relative_start": 4.011847019195557, "end": **********.178209, "relative_end": **********.178209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.17864, "relative_start": 4.012277841567993, "end": **********.17864, "relative_end": **********.17864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.17887, "relative_start": 4.012507915496826, "end": **********.17887, "relative_end": **********.17887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.179117, "relative_start": 4.012754917144775, "end": **********.179117, "relative_end": **********.179117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.179427, "relative_start": 4.013064861297607, "end": **********.179427, "relative_end": **********.179427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.179789, "relative_start": 4.013427019119263, "end": **********.179789, "relative_end": **********.179789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.180117, "relative_start": 4.013754844665527, "end": **********.180117, "relative_end": **********.180117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.180457, "relative_start": 4.014095067977905, "end": **********.180457, "relative_end": **********.180457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.180908, "relative_start": 4.014545917510986, "end": **********.180908, "relative_end": **********.180908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.18113, "relative_start": 4.01476788520813, "end": **********.18113, "relative_end": **********.18113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.181376, "relative_start": 4.015013933181763, "end": **********.181376, "relative_end": **********.181376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.181717, "relative_start": 4.015354871749878, "end": **********.181717, "relative_end": **********.181717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.182083, "relative_start": 4.015720844268799, "end": **********.182083, "relative_end": **********.182083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.182411, "relative_start": 4.016048908233643, "end": **********.182411, "relative_end": **********.182411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.182759, "relative_start": 4.016396999359131, "end": **********.182759, "relative_end": **********.182759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.183054, "relative_start": 4.0166919231414795, "end": **********.183054, "relative_end": **********.183054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.183475, "relative_start": 4.017112970352173, "end": **********.183475, "relative_end": **********.183475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.1837, "relative_start": 4.017338037490845, "end": **********.1837, "relative_end": **********.1837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.183947, "relative_start": 4.017585039138794, "end": **********.183947, "relative_end": **********.183947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.184259, "relative_start": 4.017896890640259, "end": **********.184259, "relative_end": **********.184259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.184601, "relative_start": 4.0182390213012695, "end": **********.184601, "relative_end": **********.184601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.184958, "relative_start": 4.018595933914185, "end": **********.184958, "relative_end": **********.184958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.185302, "relative_start": 4.018939971923828, "end": **********.185302, "relative_end": **********.185302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.185743, "relative_start": 4.019381046295166, "end": **********.185743, "relative_end": **********.185743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.185988, "relative_start": 4.019625902175903, "end": **********.185988, "relative_end": **********.185988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.186235, "relative_start": 4.0198729038238525, "end": **********.186235, "relative_end": **********.186235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.186543, "relative_start": 4.020180940628052, "end": **********.186543, "relative_end": **********.186543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.187073, "relative_start": 4.0207109451293945, "end": **********.187073, "relative_end": **********.187073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.187554, "relative_start": 4.021191835403442, "end": **********.187554, "relative_end": **********.187554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.18796, "relative_start": 4.021597862243652, "end": **********.18796, "relative_end": **********.18796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.188394, "relative_start": 4.022032022476196, "end": **********.188394, "relative_end": **********.188394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.188617, "relative_start": 4.022254943847656, "end": **********.188617, "relative_end": **********.188617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.188858, "relative_start": 4.022495985031128, "end": **********.188858, "relative_end": **********.188858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.alert", "start": **********.189176, "relative_start": 4.022814035415649, "end": **********.189176, "relative_end": **********.189176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.189565, "relative_start": 4.023202896118164, "end": **********.189565, "relative_end": **********.189565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::71e371662f90e963b363342e7e4f0ed5", "start": **********.190047, "relative_start": 4.023684978485107, "end": **********.190047, "relative_end": **********.190047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.190412, "relative_start": 4.024049997329712, "end": **********.190412, "relative_end": **********.190412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.190878, "relative_start": 4.024515867233276, "end": **********.190878, "relative_end": **********.190878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.191194, "relative_start": 4.024832010269165, "end": **********.191194, "relative_end": **********.191194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.191418, "relative_start": 4.025055885314941, "end": **********.191418, "relative_end": **********.191418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.19167, "relative_start": 4.025307893753052, "end": **********.19167, "relative_end": **********.19167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.191991, "relative_start": 4.025629043579102, "end": **********.191991, "relative_end": **********.191991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.192459, "relative_start": 4.026097059249878, "end": **********.192459, "relative_end": **********.192459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.192879, "relative_start": 4.026516914367676, "end": **********.192879, "relative_end": **********.192879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.193122, "relative_start": 4.02675986289978, "end": **********.193122, "relative_end": **********.193122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.193371, "relative_start": 4.027009010314941, "end": **********.193371, "relative_end": **********.193371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.193659, "relative_start": 4.027297019958496, "end": **********.193659, "relative_end": **********.193659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.193976, "relative_start": 4.027613878250122, "end": **********.193976, "relative_end": **********.193976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.194393, "relative_start": 4.028030872344971, "end": **********.194393, "relative_end": **********.194393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.194616, "relative_start": 4.02825403213501, "end": **********.194616, "relative_end": **********.194616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.194863, "relative_start": 4.028501033782959, "end": **********.194863, "relative_end": **********.194863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-radio", "start": **********.195279, "relative_start": 4.028916835784912, "end": **********.195279, "relative_end": **********.195279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.196071, "relative_start": 4.0297088623046875, "end": **********.196071, "relative_end": **********.196071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.196436, "relative_start": 4.030073881149292, "end": **********.196436, "relative_end": **********.196436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-radio", "start": **********.196914, "relative_start": 4.030551910400391, "end": **********.196914, "relative_end": **********.196914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.197702, "relative_start": 4.031339883804321, "end": **********.197702, "relative_end": **********.197702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.198333, "relative_start": 4.031970977783203, "end": **********.198333, "relative_end": **********.198333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.19876, "relative_start": 4.032397985458374, "end": **********.19876, "relative_end": **********.19876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.198984, "relative_start": 4.03262186050415, "end": **********.198984, "relative_end": **********.198984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.199232, "relative_start": 4.032870054244995, "end": **********.199232, "relative_end": **********.199232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": **********.199549, "relative_start": 4.033186912536621, "end": **********.199549, "relative_end": **********.199549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": **********.199882, "relative_start": 4.033519983291626, "end": **********.199882, "relative_end": **********.199882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": **********.200197, "relative_start": 4.033834934234619, "end": **********.200197, "relative_end": **********.200197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.200488, "relative_start": 4.034126043319702, "end": **********.200488, "relative_end": **********.200488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.201222, "relative_start": 4.034859895706177, "end": **********.201222, "relative_end": **********.201222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.201639, "relative_start": 4.035276889801025, "end": **********.201639, "relative_end": **********.201639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.20185, "relative_start": 4.03548789024353, "end": **********.20185, "relative_end": **********.20185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.202078, "relative_start": 4.0357160568237305, "end": **********.202078, "relative_end": **********.202078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.202478, "relative_start": 4.036115884780884, "end": **********.202478, "relative_end": **********.202478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.202908, "relative_start": 4.036545991897583, "end": **********.202908, "relative_end": **********.202908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.203463, "relative_start": 4.0371010303497314, "end": **********.203463, "relative_end": **********.203463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.203707, "relative_start": 4.037344932556152, "end": **********.203707, "relative_end": **********.203707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.203942, "relative_start": 4.0375800132751465, "end": **********.203942, "relative_end": **********.203942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.204229, "relative_start": 4.037867069244385, "end": **********.204229, "relative_end": **********.204229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.204539, "relative_start": 4.038177013397217, "end": **********.204539, "relative_end": **********.204539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.204953, "relative_start": 4.038590908050537, "end": **********.204953, "relative_end": **********.204953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.205169, "relative_start": 4.038806915283203, "end": **********.205169, "relative_end": **********.205169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.205397, "relative_start": 4.039034843444824, "end": **********.205397, "relative_end": **********.205397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.205665, "relative_start": 4.0393030643463135, "end": **********.205665, "relative_end": **********.205665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.205964, "relative_start": 4.039602041244507, "end": **********.205964, "relative_end": **********.205964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.206362, "relative_start": 4.039999961853027, "end": **********.206362, "relative_end": **********.206362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.206567, "relative_start": 4.040205001831055, "end": **********.206567, "relative_end": **********.206567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.206817, "relative_start": 4.040454864501953, "end": **********.206817, "relative_end": **********.206817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.207108, "relative_start": 4.040745973587036, "end": **********.207108, "relative_end": **********.207108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.207406, "relative_start": 4.041043996810913, "end": **********.207406, "relative_end": **********.207406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.207803, "relative_start": 4.041440963745117, "end": **********.207803, "relative_end": **********.207803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.208004, "relative_start": 4.0416419506073, "end": **********.208004, "relative_end": **********.208004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.208231, "relative_start": 4.0418689250946045, "end": **********.208231, "relative_end": **********.208231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.208529, "relative_start": 4.0421669483184814, "end": **********.208529, "relative_end": **********.208529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.208884, "relative_start": 4.042521953582764, "end": **********.208884, "relative_end": **********.208884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.209291, "relative_start": 4.04292893409729, "end": **********.209291, "relative_end": **********.209291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.209542, "relative_start": 4.043179988861084, "end": **********.209542, "relative_end": **********.209542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.20977, "relative_start": 4.043407917022705, "end": **********.20977, "relative_end": **********.20977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.210084, "relative_start": 4.043721914291382, "end": **********.210084, "relative_end": **********.210084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.21043, "relative_start": 4.044067859649658, "end": **********.21043, "relative_end": **********.21043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.21083, "relative_start": 4.044467926025391, "end": **********.21083, "relative_end": **********.21083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.211033, "relative_start": 4.044671058654785, "end": **********.211033, "relative_end": **********.211033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.211269, "relative_start": 4.044906854629517, "end": **********.211269, "relative_end": **********.211269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.211584, "relative_start": 4.045222043991089, "end": **********.211584, "relative_end": **********.211584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.211908, "relative_start": 4.045546054840088, "end": **********.211908, "relative_end": **********.211908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.212331, "relative_start": 4.045969009399414, "end": **********.212331, "relative_end": **********.212331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.212566, "relative_start": 4.046203851699829, "end": **********.212566, "relative_end": **********.212566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.212823, "relative_start": 4.046460866928101, "end": **********.212823, "relative_end": **********.212823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.213163, "relative_start": 4.046800851821899, "end": **********.213163, "relative_end": **********.213163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.213529, "relative_start": 4.047167062759399, "end": **********.213529, "relative_end": **********.213529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.213949, "relative_start": 4.047586917877197, "end": **********.213949, "relative_end": **********.213949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.214181, "relative_start": 4.047818899154663, "end": **********.214181, "relative_end": **********.214181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.214439, "relative_start": 4.048076868057251, "end": **********.214439, "relative_end": **********.214439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.214768, "relative_start": 4.048405885696411, "end": **********.214768, "relative_end": **********.214768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.215136, "relative_start": 4.048774003982544, "end": **********.215136, "relative_end": **********.215136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.215555, "relative_start": 4.049192905426025, "end": **********.215555, "relative_end": **********.215555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.215785, "relative_start": 4.049422979354858, "end": **********.215785, "relative_end": **********.215785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.21604, "relative_start": 4.049677848815918, "end": **********.21604, "relative_end": **********.21604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.216356, "relative_start": 4.049993991851807, "end": **********.216356, "relative_end": **********.216356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.216678, "relative_start": 4.050315856933594, "end": **********.216678, "relative_end": **********.216678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.217096, "relative_start": 4.050734043121338, "end": **********.217096, "relative_end": **********.217096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.217326, "relative_start": 4.050963878631592, "end": **********.217326, "relative_end": **********.217326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.21758, "relative_start": 4.051218032836914, "end": **********.21758, "relative_end": **********.21758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.217896, "relative_start": 4.051533937454224, "end": **********.217896, "relative_end": **********.217896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.219745, "relative_start": 4.053382873535156, "end": **********.219745, "relative_end": **********.219745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.220538, "relative_start": 4.054175853729248, "end": **********.220538, "relative_end": **********.220538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.220976, "relative_start": 4.054614067077637, "end": **********.220976, "relative_end": **********.220976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.22147, "relative_start": 4.055108070373535, "end": **********.22147, "relative_end": **********.22147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.222, "relative_start": 4.055637836456299, "end": **********.222, "relative_end": **********.222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.222568, "relative_start": 4.056205987930298, "end": **********.222568, "relative_end": **********.222568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.223203, "relative_start": 4.056840896606445, "end": **********.223203, "relative_end": **********.223203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.223545, "relative_start": 4.057183027267456, "end": **********.223545, "relative_end": **********.223545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.224046, "relative_start": 4.057683944702148, "end": **********.224046, "relative_end": **********.224046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.224547, "relative_start": 4.058184862136841, "end": **********.224547, "relative_end": **********.224547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.225019, "relative_start": 4.058656930923462, "end": **********.225019, "relative_end": **********.225019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.225764, "relative_start": 4.059401988983154, "end": **********.225764, "relative_end": **********.225764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.226091, "relative_start": 4.0597288608551025, "end": **********.226091, "relative_end": **********.226091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.22645, "relative_start": 4.0600879192352295, "end": **********.22645, "relative_end": **********.22645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.226934, "relative_start": 4.060571908950806, "end": **********.226934, "relative_end": **********.226934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.227468, "relative_start": 4.061105966567993, "end": **********.227468, "relative_end": **********.227468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.2281, "relative_start": 4.061738014221191, "end": **********.2281, "relative_end": **********.2281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.228417, "relative_start": 4.062054872512817, "end": **********.228417, "relative_end": **********.228417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.228765, "relative_start": 4.062402963638306, "end": **********.228765, "relative_end": **********.228765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.22921, "relative_start": 4.062847852706909, "end": **********.22921, "relative_end": **********.22921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.229715, "relative_start": 4.063353061676025, "end": **********.229715, "relative_end": **********.229715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.230303, "relative_start": 4.06394100189209, "end": **********.230303, "relative_end": **********.230303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.230594, "relative_start": 4.064231872558594, "end": **********.230594, "relative_end": **********.230594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.230928, "relative_start": 4.064565896987915, "end": **********.230928, "relative_end": **********.230928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.231375, "relative_start": 4.0650129318237305, "end": **********.231375, "relative_end": **********.231375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.231898, "relative_start": 4.065536022186279, "end": **********.231898, "relative_end": **********.231898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.232386, "relative_start": 4.0660240650177, "end": **********.232386, "relative_end": **********.232386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.232754, "relative_start": 4.066391944885254, "end": **********.232754, "relative_end": **********.232754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.233033, "relative_start": 4.066670894622803, "end": **********.233033, "relative_end": **********.233033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.233437, "relative_start": 4.06707501411438, "end": **********.233437, "relative_end": **********.233437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.233758, "relative_start": 4.067395925521851, "end": **********.233758, "relative_end": **********.233758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.2341, "relative_start": 4.067738056182861, "end": **********.2341, "relative_end": **********.2341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.alert", "start": **********.234606, "relative_start": 4.068243980407715, "end": **********.234606, "relative_end": **********.234606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.235065, "relative_start": 4.068702936172485, "end": **********.235065, "relative_end": **********.235065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920a4a8abfd99078c24a8f8db36a19f4", "start": **********.236169, "relative_start": 4.069807052612305, "end": **********.236169, "relative_end": **********.236169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.236482, "relative_start": 4.070119857788086, "end": **********.236482, "relative_end": **********.236482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.237048, "relative_start": 4.070685863494873, "end": **********.237048, "relative_end": **********.237048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.237378, "relative_start": 4.07101583480835, "end": **********.237378, "relative_end": **********.237378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.237614, "relative_start": 4.07125186920166, "end": **********.237614, "relative_end": **********.237614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.237873, "relative_start": 4.0715110301971436, "end": **********.237873, "relative_end": **********.237873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.alert", "start": **********.238198, "relative_start": 4.071835994720459, "end": **********.238198, "relative_end": **********.238198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.238605, "relative_start": 4.072242975234985, "end": **********.238605, "relative_end": **********.238605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::71e371662f90e963b363342e7e4f0ed5", "start": **********.239102, "relative_start": 4.072739839553833, "end": **********.239102, "relative_end": **********.239102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.239339, "relative_start": 4.072977066040039, "end": **********.239339, "relative_end": **********.239339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.239798, "relative_start": 4.07343602180481, "end": **********.239798, "relative_end": **********.239798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.240102, "relative_start": 4.073740005493164, "end": **********.240102, "relative_end": **********.240102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.240368, "relative_start": 4.074005842208862, "end": **********.240368, "relative_end": **********.240368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.240626, "relative_start": 4.074264049530029, "end": **********.240626, "relative_end": **********.240626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.240981, "relative_start": 4.0746190547943115, "end": **********.240981, "relative_end": **********.240981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.24129, "relative_start": 4.074928045272827, "end": **********.24129, "relative_end": **********.24129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.2417, "relative_start": 4.075337886810303, "end": **********.2417, "relative_end": **********.2417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.241921, "relative_start": 4.07555890083313, "end": **********.241921, "relative_end": **********.241921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.242176, "relative_start": 4.0758140087127686, "end": **********.242176, "relative_end": **********.242176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.242529, "relative_start": 4.076166868209839, "end": **********.242529, "relative_end": **********.242529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.242873, "relative_start": 4.076510906219482, "end": **********.242873, "relative_end": **********.242873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.243297, "relative_start": 4.076935052871704, "end": **********.243297, "relative_end": **********.243297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.243524, "relative_start": 4.077162027359009, "end": **********.243524, "relative_end": **********.243524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.243879, "relative_start": 4.077517032623291, "end": **********.243879, "relative_end": **********.243879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 4d69f58c2cfdff5049123ae0e3ca253b::section", "start": **********.24437, "relative_start": 4.078007936477661, "end": **********.24437, "relative_end": **********.24437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.428054, "relative_start": 4.261692047119141, "end": **********.428054, "relative_end": **********.428054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.428426, "relative_start": 4.262063980102539, "end": **********.428426, "relative_end": **********.428426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.585341, "relative_start": 4.***************, "end": **********.585341, "relative_end": **********.585341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.587641, "relative_start": 4.***************, "end": **********.587641, "relative_end": **********.587641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.588495, "relative_start": 4.422132968902588, "end": **********.588495, "relative_end": **********.588495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.589221, "relative_start": 4.422858953475952, "end": **********.589221, "relative_end": **********.589221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.590035, "relative_start": 4.423672914505005, "end": **********.590035, "relative_end": **********.590035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.590569, "relative_start": 4.424206972122192, "end": **********.590569, "relative_end": **********.590569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.592916, "relative_start": 4.426553964614868, "end": **********.592916, "relative_end": **********.592916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.593992, "relative_start": 4.4276299476623535, "end": **********.593992, "relative_end": **********.593992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.594514, "relative_start": 4.428151845932007, "end": **********.594514, "relative_end": **********.594514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.596346, "relative_start": 4.429983854293823, "end": **********.596346, "relative_end": **********.596346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.596665, "relative_start": 4.430302858352661, "end": **********.596665, "relative_end": **********.596665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.60862, "relative_start": 4.442257881164551, "end": **********.60862, "relative_end": **********.60862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.609314, "relative_start": 4.442951917648315, "end": **********.609314, "relative_end": **********.609314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.610682, "relative_start": 4.4443199634552, "end": **********.610682, "relative_end": **********.610682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.611309, "relative_start": 4.444947004318237, "end": **********.611309, "relative_end": **********.611309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.611798, "relative_start": 4.445436000823975, "end": **********.611798, "relative_end": **********.611798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.61297, "relative_start": 4.446608066558838, "end": **********.61297, "relative_end": **********.61297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.614676, "relative_start": 4.44831395149231, "end": **********.614676, "relative_end": **********.614676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.615112, "relative_start": 4.448750019073486, "end": **********.615112, "relative_end": **********.615112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.616859, "relative_start": 4.4504969120025635, "end": **********.616859, "relative_end": **********.616859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.618031, "relative_start": 4.451668977737427, "end": **********.618031, "relative_end": **********.618031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.618834, "relative_start": 4.452471971511841, "end": **********.618834, "relative_end": **********.618834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.62125, "relative_start": 4.454887866973877, "end": **********.62125, "relative_end": **********.62125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.622461, "relative_start": 4.***************, "end": **********.622461, "relative_end": **********.622461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.623221, "relative_start": 4.**************, "end": **********.623221, "relative_end": **********.623221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.623789, "relative_start": 4.***************, "end": **********.623789, "relative_end": **********.623789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.624078, "relative_start": 4.**************, "end": **********.624078, "relative_end": **********.624078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.642732, "relative_start": 4.***************, "end": **********.642732, "relative_end": **********.642732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.645409, "relative_start": 4.***************, "end": **********.645409, "relative_end": **********.645409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.6495, "relative_start": 4.***************, "end": **********.6495, "relative_end": **********.6495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.650513, "relative_start": 4.4841508865356445, "end": **********.650513, "relative_end": **********.650513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.650831, "relative_start": 4.484468936920166, "end": **********.650831, "relative_end": **********.650831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.651821, "relative_start": 4.485458850860596, "end": **********.651821, "relative_end": **********.651821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.652101, "relative_start": 4.48573899269104, "end": **********.652101, "relative_end": **********.652101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.652807, "relative_start": 4.48644495010376, "end": **********.652807, "relative_end": **********.652807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.653853, "relative_start": 4.487490892410278, "end": **********.653853, "relative_end": **********.653853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.654101, "relative_start": 4.487738847732544, "end": **********.654101, "relative_end": **********.654101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.655579, "relative_start": 4.489217042922974, "end": **********.655579, "relative_end": **********.655579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.656179, "relative_start": 4.489816904067993, "end": **********.656179, "relative_end": **********.656179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.656493, "relative_start": 4.49013090133667, "end": **********.656493, "relative_end": **********.656493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.657035, "relative_start": 4.490673065185547, "end": **********.657035, "relative_end": **********.657035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.657283, "relative_start": 4.4909210205078125, "end": **********.657283, "relative_end": **********.657283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.657796, "relative_start": 4.49143385887146, "end": **********.657796, "relative_end": **********.657796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.658091, "relative_start": 4.491729021072388, "end": **********.658091, "relative_end": **********.658091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.675207, "relative_start": 4.50884485244751, "end": **********.675207, "relative_end": **********.675207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.67606, "relative_start": 4.509697914123535, "end": **********.67606, "relative_end": **********.67606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.677527, "relative_start": 4.511164903640747, "end": **********.677527, "relative_end": **********.677527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.678112, "relative_start": 4.511749982833862, "end": **********.678112, "relative_end": **********.678112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.678835, "relative_start": 4.512472867965698, "end": **********.678835, "relative_end": **********.678835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c50817c59f218fea1b6c19be4db347dd", "start": **********.680675, "relative_start": 4.514312982559204, "end": **********.680675, "relative_end": **********.680675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.681306, "relative_start": 4.514943838119507, "end": **********.681306, "relative_end": **********.681306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.681747, "relative_start": 4.515384912490845, "end": **********.681747, "relative_end": **********.681747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.682272, "relative_start": 4.515909910202026, "end": **********.682272, "relative_end": **********.682272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42d71f4fe12493bd6829a606380e1435", "start": **********.684315, "relative_start": 4.517952919006348, "end": **********.684315, "relative_end": **********.684315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.685035, "relative_start": 4.518672943115234, "end": **********.685035, "relative_end": **********.685035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1a65d1071503a2e05e73a706bfebac58", "start": **********.687844, "relative_start": 4.521481990814209, "end": **********.687844, "relative_end": **********.687844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.688432, "relative_start": 4.522069931030273, "end": **********.688432, "relative_end": **********.688432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.688789, "relative_start": 4.5224268436431885, "end": **********.688789, "relative_end": **********.688789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.68928, "relative_start": 4.522917985916138, "end": **********.68928, "relative_end": **********.68928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e33c8bf87ccc47b22832a4eb415ca9d", "start": **********.690832, "relative_start": 4.52446985244751, "end": **********.690832, "relative_end": **********.690832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.691545, "relative_start": 4.5251829624176025, "end": **********.691545, "relative_end": **********.691545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::452ccf81d5bbacec7ec062999540e293", "start": **********.692919, "relative_start": 4.526556968688965, "end": **********.692919, "relative_end": **********.692919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.693553, "relative_start": 4.527190923690796, "end": **********.693553, "relative_end": **********.693553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.693878, "relative_start": 4.527515888214111, "end": **********.693878, "relative_end": **********.693878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.694372, "relative_start": 4.52800989151001, "end": **********.694372, "relative_end": **********.694372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ee52ba22bc1262334c4146935fbed227", "start": **********.696833, "relative_start": 4.530470848083496, "end": **********.696833, "relative_end": **********.696833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.697462, "relative_start": 4.531100034713745, "end": **********.697462, "relative_end": **********.697462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7bd0601fe0dc4645b269458a9ed5f144", "start": **********.699277, "relative_start": 4.532914876937866, "end": **********.699277, "relative_end": **********.699277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.699847, "relative_start": 4.533484935760498, "end": **********.699847, "relative_end": **********.699847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.70111, "relative_start": 4.534747838973999, "end": **********.70111, "relative_end": **********.70111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.701685, "relative_start": 4.535322904586792, "end": **********.701685, "relative_end": **********.701685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.702044, "relative_start": 4.535681962966919, "end": **********.702044, "relative_end": **********.702044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.702568, "relative_start": 4.536206007003784, "end": **********.702568, "relative_end": **********.702568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3de92deb18074b63f585befac7d0965", "start": **********.704427, "relative_start": 4.538064956665039, "end": **********.704427, "relative_end": **********.704427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.70509, "relative_start": 4.5387279987335205, "end": **********.70509, "relative_end": **********.70509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69ab4d8cea5c292eefba375a85e9768a", "start": **********.706838, "relative_start": 4.540475845336914, "end": **********.706838, "relative_end": **********.706838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.7075, "relative_start": 4.541137933731079, "end": **********.7075, "relative_end": **********.7075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::64f291ea9b97f679266b5e47f5ae8464", "start": **********.708746, "relative_start": 4.542383909225464, "end": **********.708746, "relative_end": **********.708746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.709292, "relative_start": 4.5429298877716064, "end": **********.709292, "relative_end": **********.709292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.710834, "relative_start": 4.544471979141235, "end": **********.710834, "relative_end": **********.710834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.711415, "relative_start": 4.545053005218506, "end": **********.711415, "relative_end": **********.711415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d3f310865558ef9371c1fb4b468d742", "start": **********.712717, "relative_start": 4.5463550090789795, "end": **********.712717, "relative_end": **********.712717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.713266, "relative_start": 4.546903848648071, "end": **********.713266, "relative_end": **********.713266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.714898, "relative_start": 4.548536062240601, "end": **********.714898, "relative_end": **********.714898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.715446, "relative_start": 4.549083948135376, "end": **********.715446, "relative_end": **********.715446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2c23a08a8a248b19ed43094ec82cfb6b", "start": **********.71708, "relative_start": 4.550718069076538, "end": **********.71708, "relative_end": **********.71708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.717623, "relative_start": 4.551260948181152, "end": **********.717623, "relative_end": **********.717623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.718842, "relative_start": 4.552479982376099, "end": **********.718842, "relative_end": **********.718842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.719412, "relative_start": 4.5530500411987305, "end": **********.719412, "relative_end": **********.719412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1859ae2c66dc0538fd83aadc7f316844", "start": **********.722429, "relative_start": 4.556066989898682, "end": **********.722429, "relative_end": **********.722429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.72316, "relative_start": 4.556797981262207, "end": **********.72316, "relative_end": **********.72316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ccda1407f97c36756562e2c01f09a7ab", "start": **********.724934, "relative_start": 4.558572053909302, "end": **********.724934, "relative_end": **********.724934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.725527, "relative_start": 4.559165000915527, "end": **********.725527, "relative_end": **********.725527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.727982, "relative_start": 4.561619997024536, "end": **********.727982, "relative_end": **********.727982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.730986, "relative_start": 4.564624071121216, "end": **********.730986, "relative_end": **********.730986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2cd2bd3a1a36cafc00007578f96771de", "start": **********.732568, "relative_start": 4.566205978393555, "end": **********.732568, "relative_end": **********.732568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.733063, "relative_start": 4.5667009353637695, "end": **********.733063, "relative_end": **********.733063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::24c50807435b9d6d46a8d65cd016c8b0", "start": **********.734625, "relative_start": 4.568263053894043, "end": **********.734625, "relative_end": **********.734625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.735107, "relative_start": 4.568744897842407, "end": **********.735107, "relative_end": **********.735107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.736599, "relative_start": 4.570236921310425, "end": **********.736599, "relative_end": **********.736599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.737112, "relative_start": 4.570749998092651, "end": **********.737112, "relative_end": **********.737112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.737779, "relative_start": 4.571416854858398, "end": **********.737779, "relative_end": **********.737779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2279ce135fc9e268c1dcef75eaca22a5", "start": **********.739599, "relative_start": 4.57323694229126, "end": **********.739599, "relative_end": **********.739599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.740167, "relative_start": 4.57380485534668, "end": **********.740167, "relative_end": **********.740167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.740476, "relative_start": 4.574113845825195, "end": **********.740476, "relative_end": **********.740476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.740902, "relative_start": 4.57453989982605, "end": **********.740902, "relative_end": **********.740902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.742107, "relative_start": 4.575744867324829, "end": **********.742107, "relative_end": **********.742107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.7426, "relative_start": 4.576237916946411, "end": **********.7426, "relative_end": **********.7426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.743291, "relative_start": 4.5769288539886475, "end": **********.743291, "relative_end": **********.743291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.743779, "relative_start": 4.577416896820068, "end": **********.743779, "relative_end": **********.743779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.744441, "relative_start": 4.578078985214233, "end": **********.744441, "relative_end": **********.744441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.74493, "relative_start": 4.578567981719971, "end": **********.74493, "relative_end": **********.74493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.746069, "relative_start": 4.579706907272339, "end": **********.746069, "relative_end": **********.746069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.746569, "relative_start": 4.580206871032715, "end": **********.746569, "relative_end": **********.746569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.746864, "relative_start": 4.580502033233643, "end": **********.746864, "relative_end": **********.746864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.74725, "relative_start": 4.580888032913208, "end": **********.74725, "relative_end": **********.74725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.747924, "relative_start": 4.581562042236328, "end": **********.747924, "relative_end": **********.747924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.748389, "relative_start": 4.582026958465576, "end": **********.748389, "relative_end": **********.748389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.749417, "relative_start": 4.583055019378662, "end": **********.749417, "relative_end": **********.749417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.749931, "relative_start": 4.583569049835205, "end": **********.749931, "relative_end": **********.749931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.750217, "relative_start": 4.583854913711548, "end": **********.750217, "relative_end": **********.750217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.750583, "relative_start": 4.584220886230469, "end": **********.750583, "relative_end": **********.750583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.751702, "relative_start": 4.5853400230407715, "end": **********.751702, "relative_end": **********.751702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.752183, "relative_start": 4.585820913314819, "end": **********.752183, "relative_end": **********.752183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.753072, "relative_start": 4.586709976196289, "end": **********.753072, "relative_end": **********.753072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.753568, "relative_start": 4.58720588684082, "end": **********.753568, "relative_end": **********.753568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.754246, "relative_start": 4.587883949279785, "end": **********.754246, "relative_end": **********.754246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.755508, "relative_start": 4.58914589881897, "end": **********.755508, "relative_end": **********.755508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.756064, "relative_start": 4.589701890945435, "end": **********.756064, "relative_end": **********.756064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.756739, "relative_start": 4.590376853942871, "end": **********.756739, "relative_end": **********.756739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::998e4178ecae37dacf7321232f455f64", "start": **********.758144, "relative_start": 4.591781854629517, "end": **********.758144, "relative_end": **********.758144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.758725, "relative_start": 4.592362880706787, "end": **********.758725, "relative_end": **********.758725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06e7cfabd119917d6efbd595a344e37b", "start": **********.760626, "relative_start": 4.594264030456543, "end": **********.760626, "relative_end": **********.760626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.761181, "relative_start": 4.594819068908691, "end": **********.761181, "relative_end": **********.761181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.762759, "relative_start": 4.5963969230651855, "end": **********.762759, "relative_end": **********.762759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.76329, "relative_start": 4.596927881240845, "end": **********.76329, "relative_end": **********.76329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.764421, "relative_start": 4.5980589389801025, "end": **********.764421, "relative_end": **********.764421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.76499, "relative_start": 4.598628044128418, "end": **********.76499, "relative_end": **********.76499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.765659, "relative_start": 4.599297046661377, "end": **********.765659, "relative_end": **********.765659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.768, "relative_start": 4.601637840270996, "end": **********.768, "relative_end": **********.768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.768566, "relative_start": 4.602203845977783, "end": **********.768566, "relative_end": **********.768566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.768893, "relative_start": 4.6025309562683105, "end": **********.768893, "relative_end": **********.768893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.769353, "relative_start": 4.6029908657073975, "end": **********.769353, "relative_end": **********.769353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.7703, "relative_start": 4.603937864303589, "end": **********.7703, "relative_end": **********.7703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.77077, "relative_start": 4.604408025741577, "end": **********.77077, "relative_end": **********.77077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.771093, "relative_start": 4.604730844497681, "end": **********.771093, "relative_end": **********.771093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.771504, "relative_start": 4.605141878128052, "end": **********.771504, "relative_end": **********.771504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.772342, "relative_start": 4.605979919433594, "end": **********.772342, "relative_end": **********.772342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.772834, "relative_start": 4.606472015380859, "end": **********.772834, "relative_end": **********.772834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.773561, "relative_start": 4.60719895362854, "end": **********.773561, "relative_end": **********.773561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.774093, "relative_start": 4.607730865478516, "end": **********.774093, "relative_end": **********.774093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.774748, "relative_start": 4.608386039733887, "end": **********.774748, "relative_end": **********.774748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.775888, "relative_start": 4.609525918960571, "end": **********.775888, "relative_end": **********.775888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.776379, "relative_start": 4.6100170612335205, "end": **********.776379, "relative_end": **********.776379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.777083, "relative_start": 4.610720872879028, "end": **********.777083, "relative_end": **********.777083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.777571, "relative_start": 4.611208915710449, "end": **********.777571, "relative_end": **********.777571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.778269, "relative_start": 4.611907005310059, "end": **********.778269, "relative_end": **********.778269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.778766, "relative_start": 4.612403869628906, "end": **********.778766, "relative_end": **********.778766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.779408, "relative_start": 4.613045930862427, "end": **********.779408, "relative_end": **********.779408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.78017, "relative_start": 4.613807916641235, "end": **********.78017, "relative_end": **********.78017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.780617, "relative_start": 4.614254951477051, "end": **********.780617, "relative_end": **********.780617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.780919, "relative_start": 4.6145570278167725, "end": **********.780919, "relative_end": **********.780919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.78134, "relative_start": 4.614977836608887, "end": **********.78134, "relative_end": **********.78134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.78253, "relative_start": 4.616168022155762, "end": **********.78253, "relative_end": **********.78253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.783028, "relative_start": 4.616665840148926, "end": **********.783028, "relative_end": **********.783028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.784201, "relative_start": 4.6178388595581055, "end": **********.784201, "relative_end": **********.784201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.784712, "relative_start": 4.618350028991699, "end": **********.784712, "relative_end": **********.784712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.785374, "relative_start": 4.619011878967285, "end": **********.785374, "relative_end": **********.785374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81d33e262b34e339fefa952b8ffa5f1", "start": **********.786606, "relative_start": 4.620244026184082, "end": **********.786606, "relative_end": **********.786606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.787119, "relative_start": 4.6207568645477295, "end": **********.787119, "relative_end": **********.787119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.787795, "relative_start": 4.6214330196380615, "end": **********.787795, "relative_end": **********.787795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.789826, "relative_start": 4.623463869094849, "end": **********.789826, "relative_end": **********.789826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.790463, "relative_start": 4.624100923538208, "end": **********.790463, "relative_end": **********.790463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fda51db955ba828a198ee1c8d52b2003", "start": **********.792022, "relative_start": 4.625659942626953, "end": **********.792022, "relative_end": **********.792022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.792549, "relative_start": 4.626186847686768, "end": **********.792549, "relative_end": **********.792549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.793605, "relative_start": 4.6272430419921875, "end": **********.793605, "relative_end": **********.793605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.794102, "relative_start": 4.627739906311035, "end": **********.794102, "relative_end": **********.794102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.794748, "relative_start": 4.6283860206604, "end": **********.794748, "relative_end": **********.794748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.79647, "relative_start": 4.630107879638672, "end": **********.79647, "relative_end": **********.79647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.797069, "relative_start": 4.630707025527954, "end": **********.797069, "relative_end": **********.797069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.797988, "relative_start": 4.6316258907318115, "end": **********.797988, "relative_end": **********.797988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.799636, "relative_start": 4.6332738399505615, "end": **********.799636, "relative_end": **********.799636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.800206, "relative_start": 4.633843898773193, "end": **********.800206, "relative_end": **********.800206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.801769, "relative_start": 4.635406970977783, "end": **********.801769, "relative_end": **********.801769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.802306, "relative_start": 4.63594388961792, "end": **********.802306, "relative_end": **********.802306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.803875, "relative_start": 4.637512922286987, "end": **********.803875, "relative_end": **********.803875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.80439, "relative_start": 4.638027906417847, "end": **********.80439, "relative_end": **********.80439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.805909, "relative_start": 4.639546871185303, "end": **********.805909, "relative_end": **********.805909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.806428, "relative_start": 4.640065908432007, "end": **********.806428, "relative_end": **********.806428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.808096, "relative_start": 4.641733884811401, "end": **********.808096, "relative_end": **********.808096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.808677, "relative_start": 4.642314910888672, "end": **********.808677, "relative_end": **********.808677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.810556, "relative_start": 4.644193887710571, "end": **********.810556, "relative_end": **********.810556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.811142, "relative_start": 4.644779920578003, "end": **********.811142, "relative_end": **********.811142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.811895, "relative_start": 4.645532846450806, "end": **********.811895, "relative_end": **********.811895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.813076, "relative_start": 4.646713972091675, "end": **********.813076, "relative_end": **********.813076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.813668, "relative_start": 4.647305965423584, "end": **********.813668, "relative_end": **********.813668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.814392, "relative_start": 4.648030042648315, "end": **********.814392, "relative_end": **********.814392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.8163, "relative_start": 4.649937868118286, "end": **********.8163, "relative_end": **********.8163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.816874, "relative_start": 4.650511980056763, "end": **********.816874, "relative_end": **********.816874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.818149, "relative_start": 4.651787042617798, "end": **********.818149, "relative_end": **********.818149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.818685, "relative_start": 4.652323007583618, "end": **********.818685, "relative_end": **********.818685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.819965, "relative_start": 4.653602838516235, "end": **********.819965, "relative_end": **********.819965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.820512, "relative_start": 4.654150009155273, "end": **********.820512, "relative_end": **********.820512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.82177, "relative_start": 4.655407905578613, "end": **********.82177, "relative_end": **********.82177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.822316, "relative_start": 4.655953884124756, "end": **********.822316, "relative_end": **********.822316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.823544, "relative_start": 4.657181978225708, "end": **********.823544, "relative_end": **********.823544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.824065, "relative_start": 4.657702922821045, "end": **********.824065, "relative_end": **********.824065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.825284, "relative_start": 4.658921957015991, "end": **********.825284, "relative_end": **********.825284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.825798, "relative_start": 4.659435987472534, "end": **********.825798, "relative_end": **********.825798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.827283, "relative_start": 4.660920858383179, "end": **********.827283, "relative_end": **********.827283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.827825, "relative_start": 4.661463022232056, "end": **********.827825, "relative_end": **********.827825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.829264, "relative_start": 4.662901878356934, "end": **********.829264, "relative_end": **********.829264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.829784, "relative_start": 4.663421869277954, "end": **********.829784, "relative_end": **********.829784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.830972, "relative_start": 4.664609909057617, "end": **********.830972, "relative_end": **********.830972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.831508, "relative_start": 4.6651458740234375, "end": **********.831508, "relative_end": **********.831508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.832149, "relative_start": 4.665786981582642, "end": **********.832149, "relative_end": **********.832149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.833691, "relative_start": 4.667328834533691, "end": **********.833691, "relative_end": **********.833691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.834182, "relative_start": 4.667819976806641, "end": **********.834182, "relative_end": **********.834182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d5f951b8390a2ab38dcc695dab7b152", "start": **********.835271, "relative_start": 4.6689088344573975, "end": **********.835271, "relative_end": **********.835271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.835754, "relative_start": 4.669391870498657, "end": **********.835754, "relative_end": **********.835754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::944e2ad490e8085f4039835ea5eefcbf", "start": **********.836932, "relative_start": 4.670569896697998, "end": **********.836932, "relative_end": **********.836932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.837445, "relative_start": 4.671082973480225, "end": **********.837445, "relative_end": **********.837445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.838112, "relative_start": 4.671750068664551, "end": **********.838112, "relative_end": **********.838112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.839201, "relative_start": 4.672838926315308, "end": **********.839201, "relative_end": **********.839201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.839693, "relative_start": 4.673331022262573, "end": **********.839693, "relative_end": **********.839693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.840806, "relative_start": 4.674443960189819, "end": **********.840806, "relative_end": **********.840806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.841321, "relative_start": 4.674958944320679, "end": **********.841321, "relative_end": **********.841321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.841972, "relative_start": 4.675610065460205, "end": **********.841972, "relative_end": **********.841972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.843133, "relative_start": 4.676770925521851, "end": **********.843133, "relative_end": **********.843133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.843767, "relative_start": 4.677404880523682, "end": **********.843767, "relative_end": **********.843767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.844457, "relative_start": 4.678094863891602, "end": **********.844457, "relative_end": **********.844457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.845533, "relative_start": 4.679170846939087, "end": **********.845533, "relative_end": **********.845533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.84619, "relative_start": 4.679827928543091, "end": **********.84619, "relative_end": **********.84619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.846544, "relative_start": 4.680181980133057, "end": **********.846544, "relative_end": **********.846544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.847436, "relative_start": 4.681073904037476, "end": **********.847436, "relative_end": **********.847436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.847759, "relative_start": 4.681396961212158, "end": **********.847759, "relative_end": **********.847759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.84856, "relative_start": 4.6821980476379395, "end": **********.84856, "relative_end": **********.84856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.848983, "relative_start": 4.682621002197266, "end": **********.848983, "relative_end": **********.848983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.850343, "relative_start": 4.683980941772461, "end": **********.850343, "relative_end": **********.850343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.850601, "relative_start": 4.684238910675049, "end": **********.850601, "relative_end": **********.850601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.851348, "relative_start": 4.684985876083374, "end": **********.851348, "relative_end": **********.851348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.851734, "relative_start": 4.6853718757629395, "end": **********.851734, "relative_end": **********.851734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.852052, "relative_start": 4.685689926147461, "end": **********.852052, "relative_end": **********.852052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.index", "start": **********.852365, "relative_start": 4.686002969741821, "end": **********.852365, "relative_end": **********.852365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.853153, "relative_start": 4.686790943145752, "end": **********.853153, "relative_end": **********.853153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.853871, "relative_start": 4.687509059906006, "end": **********.853871, "relative_end": **********.853871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.854592, "relative_start": 4.688230037689209, "end": **********.854592, "relative_end": **********.854592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.854876, "relative_start": 4.688513994216919, "end": **********.854876, "relative_end": **********.854876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::custom-template", "start": **********.855636, "relative_start": 4.689273834228516, "end": **********.855636, "relative_end": **********.855636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.856029, "relative_start": 4.689666986465454, "end": **********.856029, "relative_end": **********.856029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.856827, "relative_start": 4.690464973449707, "end": **********.856827, "relative_end": **********.856827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.85714, "relative_start": 4.690778017044067, "end": **********.85714, "relative_end": **********.85714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.85757, "relative_start": 4.6912078857421875, "end": **********.85757, "relative_end": **********.85757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.858294, "relative_start": 4.691931962966919, "end": **********.858294, "relative_end": **********.858294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.85889, "relative_start": 4.692528009414673, "end": **********.85889, "relative_end": **********.85889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.859264, "relative_start": 4.692901849746704, "end": **********.859264, "relative_end": **********.859264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.859628, "relative_start": 4.693265914916992, "end": **********.859628, "relative_end": **********.859628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.860568, "relative_start": 4.69420599937439, "end": **********.860568, "relative_end": **********.860568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.861144, "relative_start": 4.694782018661499, "end": **********.861144, "relative_end": **********.861144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.861653, "relative_start": 4.695291042327881, "end": **********.861653, "relative_end": **********.861653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.862321, "relative_start": 4.695958852767944, "end": **********.862321, "relative_end": **********.862321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.862689, "relative_start": 4.696326971054077, "end": **********.862689, "relative_end": **********.862689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::debug-badge", "start": **********.0359, "relative_start": 4.869538068771362, "end": **********.0359, "relative_end": **********.0359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.036463, "relative_start": 4.870100975036621, "end": **********.036463, "relative_end": **********.036463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.037102, "relative_start": 4.870739936828613, "end": **********.037102, "relative_end": **********.037102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.037713, "relative_start": 4.871351003646851, "end": **********.037713, "relative_end": **********.037713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.038385, "relative_start": 4.872022867202759, "end": **********.038385, "relative_end": **********.038385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.038666, "relative_start": 4.8723039627075195, "end": **********.038666, "relative_end": **********.038666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.039509, "relative_start": 4.873147010803223, "end": **********.039509, "relative_end": **********.039509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.040084, "relative_start": 4.8737218379974365, "end": **********.040084, "relative_end": **********.040084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.040683, "relative_start": 4.874320983886719, "end": **********.040683, "relative_end": **********.040683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.041007, "relative_start": 4.874644994735718, "end": **********.041007, "relative_end": **********.041007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.041231, "relative_start": 4.874868869781494, "end": **********.041231, "relative_end": **********.041231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::layouts.base", "start": **********.042182, "relative_start": 4.87581992149353, "end": **********.042182, "relative_end": **********.042182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.042962, "relative_start": 4.876600027084351, "end": **********.042962, "relative_end": **********.042962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.045147, "relative_start": 4.878784894943237, "end": **********.045147, "relative_end": **********.045147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.047053, "relative_start": 4.880691051483154, "end": **********.047053, "relative_end": **********.047053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.049457, "relative_start": 4.883095026016235, "end": **********.049457, "relative_end": **********.049457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.050299, "relative_start": 4.883936882019043, "end": **********.050299, "relative_end": **********.050299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 71124672, "peak_usage_str": "68MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1107, "nb_templates": 1107, "templates": [{"name": "2x core/setting::forms.partials.action", "param_count": null, "params": [], "start": **********.88001, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/forms/partials/action.blade.phpcore/setting::forms.partials.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fforms%2Fpartials%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/setting::forms.partials.action"}, {"name": "2x 4d69f58c2cfdff5049123ae0e3ca253b::section.action.save", "param_count": null, "params": [], "start": **********.974873, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/components/section/action/save.blade.php4d69f58c2cfdff5049123ae0e3ca253b::section.action.save", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fsection%2Faction%2Fsave.blade.php&line=1", "ajax": false, "filename": "save.blade.php", "line": "?"}, "render_count": 2, "name_original": "4d69f58c2cfdff5049123ae0e3ca253b::section.action.save"}, {"name": "8x ********************************::button", "param_count": null, "params": [], "start": **********.161969, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 8, "name_original": "********************************::button"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.164456, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x 4d69f58c2cfdff5049123ae0e3ca253b::section.action.index", "param_count": null, "params": [], "start": **********.164801, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/components/section/action/index.blade.php4d69f58c2cfdff5049123ae0e3ca253b::section.action.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fsection%2Faction%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "4d69f58c2cfdff5049123ae0e3ca253b::section.action.index"}, {"name": "1x core/setting::partials.media.action-buttons", "param_count": null, "params": [], "start": **********.217935, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/partials/media/action-buttons.blade.phpcore/setting::partials.media.action-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fpartials%2Fmedia%2Faction-buttons.blade.php&line=1", "ajax": false, "filename": "action-buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/setting::partials.media.action-buttons"}, {"name": "1x core/setting::partials.media.chunk-size-upload-field", "param_count": null, "params": [], "start": **********.504167, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/partials/media/chunk-size-upload-field.blade.phpcore/setting::partials.media.chunk-size-upload-field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fpartials%2Fmedia%2Fchunk-size-upload-field.blade.php&line=1", "ajax": false, "filename": "chunk-size-upload-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/setting::partials.media.chunk-size-upload-field"}, {"name": "12x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": 1749837073.094442, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 12, "name_original": "********************************::form.on-off.checkbox"}, {"name": "12x core/base::components.form.checkbox", "param_count": null, "params": [], "start": 1749837073.239052, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 12, "name_original": "core/base::components.form.checkbox"}, {"name": "26x ********************************::form-group", "param_count": null, "params": [], "start": 1749837073.240243, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 26, "name_original": "********************************::form-group"}, {"name": "5x ********************************::form.text-input", "param_count": null, "params": [], "start": 1749837073.240768, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/text-input.blade.php********************************::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form.text-input"}, {"name": "63x ********************************::form.label", "param_count": null, "params": [], "start": 1749837073.242411, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 63, "name_original": "********************************::form.label"}, {"name": "5x ********************************::form.error", "param_count": null, "params": [], "start": 1749837073.242846, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/error.blade.php********************************::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form.error"}, {"name": "2x ********************************::form.fieldset", "param_count": null, "params": [], "start": 1749837073.245487, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/fieldset.blade.php********************************::form.fieldset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffieldset.blade.php&line=1", "ajax": false, "filename": "fieldset.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.fieldset"}, {"name": "20x 4d69f58c2cfdff5049123ae0e3ca253b::form-group", "param_count": null, "params": [], "start": 1749837073.269674, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/components/form-group.blade.php4d69f58c2cfdff5049123ae0e3ca253b::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 20, "name_original": "4d69f58c2cfdff5049123ae0e3ca253b::form-group"}, {"name": "1x core/setting::partials.media.media-folders-can-add-watermark-field", "param_count": null, "params": [], "start": 1749837073.366485, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/partials/media/media-folders-can-add-watermark-field.blade.phpcore/setting::partials.media.media-folders-can-add-watermark-field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fpartials%2Fmedia%2Fmedia-folders-can-add-watermark-field.blade.php&line=1", "ajax": false, "filename": "media-folders-can-add-watermark-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/setting::partials.media.media-folders-can-add-watermark-field"}, {"name": "19x ********************************::form.checkbox", "param_count": null, "params": [], "start": 1749837074.011752, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 19, "name_original": "********************************::form.checkbox"}, {"name": "4x core/setting::includes.form-media-size-label", "param_count": null, "params": [], "start": 1749837074.043472, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/includes/form-media-size-label.blade.phpcore/setting::includes.form-media-size-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fincludes%2Fform-media-size-label.blade.php&line=1", "ajax": false, "filename": "form-media-size-label.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/setting::includes.form-media-size-label"}, {"name": "1x core/setting::media", "param_count": null, "params": [], "start": 1749837074.179391, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/media.blade.phpcore/setting::media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/setting::media"}, {"name": "1x core/setting::forms.form-content-only", "param_count": null, "params": [], "start": 1749837074.303836, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/forms/form-content-only.blade.phpcore/setting::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/setting::forms.form-content-only"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": 1749837074.428477, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php&line=1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "8x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": 1749837074.429424, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.fields.custom-select"}, {"name": "49x core/base::forms.partials.label", "param_count": null, "params": [], "start": 1749837074.430314, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 49, "name_original": "core/base::forms.partials.label"}, {"name": "8x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": 1749837074.431455, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.partials.custom-select"}, {"name": "113x ********************************::form.field", "param_count": null, "params": [], "start": 1749837074.431926, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 113, "name_original": "********************************::form.field"}, {"name": "113x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1749837074.432859, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 113, "name_original": "core/base::forms.partials.help-block"}, {"name": "113x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1749837074.433293, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 113, "name_original": "core/base::forms.partials.errors"}, {"name": "113x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1749837074.433745, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 113, "name_original": "core/base::forms.columns.column-span"}, {"name": "41x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1749837074.434701, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 41, "name_original": "core/base::forms.fields.html"}, {"name": "31x core/base::forms.fields.text", "param_count": null, "params": [], "start": 1749837074.438914, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 31, "name_original": "core/base::forms.fields.text"}, {"name": "11x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": 1749837074.48715, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php&line=1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "11x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": 1749837074.581626, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php&line=1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "2x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": 1749837074.777087, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php&line=1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.media-image"}, {"name": "2x core/base::forms.partials.image", "param_count": null, "params": [], "start": 1749837074.779128, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.image"}, {"name": "2x ********************************::form.image", "param_count": null, "params": [], "start": 1749837074.7801, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/image.blade.php********************************::form.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.image"}, {"name": "2x ********************************::image", "param_count": null, "params": [], "start": 1749837074.781784, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/image.blade.php********************************::image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::image"}, {"name": "2x __components::1c3e3c2b5f3306f2c083e21b9339fe63", "param_count": null, "params": [], "start": 1749837074.784324, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1c3e3c2b5f3306f2c083e21b9339fe63.blade.php__components::1c3e3c2b5f3306f2c083e21b9339fe63", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1c3e3c2b5f3306f2c083e21b9339fe63.blade.php&line=1", "ajax": false, "filename": "1c3e3c2b5f3306f2c083e21b9339fe63.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1c3e3c2b5f3306f2c083e21b9339fe63"}, {"name": "15x core/base::forms.fields.number", "param_count": null, "params": [], "start": 1749837074.788301, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/number.blade.phpcore/base::forms.fields.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 15, "name_original": "core/base::forms.fields.number"}, {"name": "4x core/base::forms.fields.alert", "param_count": null, "params": [], "start": 1749837074.934683, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/alert.blade.phpcore/base::forms.fields.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.fields.alert"}, {"name": "4x ********************************::alert", "param_count": null, "params": [], "start": **********.126579, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/alert.blade.php********************************::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::alert"}, {"name": "3x __components::71e371662f90e963b363342e7e4f0ed5", "param_count": null, "params": [], "start": **********.12827, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/71e371662f90e963b363342e7e4f0ed5.blade.php__components::71e371662f90e963b363342e7e4f0ed5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F71e371662f90e963b363342e7e4f0ed5.blade.php&line=1", "ajax": false, "filename": "71e371662f90e963b363342e7e4f0ed5.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::71e371662f90e963b363342e7e4f0ed5"}, {"name": "1x core/base::forms.fields.custom-radio", "param_count": null, "params": [], "start": **********.195258, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/custom-radio.blade.phpcore/base::forms.fields.custom-radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-radio.blade.php&line=1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-radio"}, {"name": "1x core/base::forms.partials.custom-radio", "param_count": null, "params": [], "start": **********.196885, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/custom-radio.blade.phpcore/base::forms.partials.custom-radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-radio.blade.php&line=1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-radio"}, {"name": "1x ********************************::form.radio", "param_count": null, "params": [], "start": **********.197682, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/radio.blade.php********************************::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.radio"}, {"name": "1x __components::920a4a8abfd99078c24a8f8db36a19f4", "param_count": null, "params": [], "start": **********.23615, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/920a4a8abfd99078c24a8f8db36a19f4.blade.php__components::920a4a8abfd99078c24a8f8db36a19f4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F920a4a8abfd99078c24a8f8db36a19f4.blade.php&line=1", "ajax": false, "filename": "920a4a8abfd99078c24a8f8db36a19f4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::920a4a8abfd99078c24a8f8db36a19f4"}, {"name": "1x 4d69f58c2cfdff5049123ae0e3ca253b::section", "param_count": null, "params": [], "start": **********.244341, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/setting/resources/views/components/section.blade.php4d69f58c2cfdff5049123ae0e3ca253b::section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fsection.blade.php&line=1", "ajax": false, "filename": "section.blade.php", "line": "?"}, "render_count": 1, "name_original": "4d69f58c2cfdff5049123ae0e3ca253b::section"}, {"name": "1x ********************************::card.body.index", "param_count": null, "params": [], "start": **********.428031, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.body.index"}, {"name": "2x ********************************::card.index", "param_count": null, "params": [], "start": **********.428408, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::card.index"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.585317, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "3x ********************************::modal.action", "param_count": null, "params": [], "start": **********.587618, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/action.blade.php********************************::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::modal.action"}, {"name": "3x ********************************::modal.alert", "param_count": null, "params": [], "start": **********.588473, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/alert.blade.php********************************::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::modal.alert"}, {"name": "5x ********************************::modal.close-button", "param_count": null, "params": [], "start": **********.589201, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/close-button.blade.php********************************::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::modal.close-button"}, {"name": "2x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.590004, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php&line=1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "5x ********************************::modal", "param_count": null, "params": [], "start": **********.590524, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal.blade.php********************************::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::modal"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.59288, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.593956, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.594479, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.596325, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.596646, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.60858, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.61464, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php&line=1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.615077, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.616839, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php&line=1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.618011, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.618814, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php&line=1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.621228, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.622441, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php&line=1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x ********************************::card.title", "param_count": null, "params": [], "start": **********.623202, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.title"}, {"name": "1x ********************************::card.actions", "param_count": null, "params": [], "start": **********.62377, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.actions"}, {"name": "1x ********************************::card.header.index", "param_count": null, "params": [], "start": **********.624058, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.header.index"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.645389, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "4x ********************************::dropdown.item", "param_count": null, "params": [], "start": **********.649481, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.php********************************::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::dropdown.item"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.650495, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php&line=1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.651802, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php&line=1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "2x ********************************::dropdown.index", "param_count": null, "params": [], "start": **********.652073, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/index.blade.php********************************::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.652767, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.657775, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.658072, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "18x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.675185, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "75x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.67604, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 75, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.677507, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::c50817c59f218fea1b6c19be4db347dd", "param_count": null, "params": [], "start": **********.680656, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c50817c59f218fea1b6c19be4db347dd.blade.php__components::c50817c59f218fea1b6c19be4db347dd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc50817c59f218fea1b6c19be4db347dd.blade.php&line=1", "ajax": false, "filename": "c50817c59f218fea1b6c19be4db347dd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c50817c59f218fea1b6c19be4db347dd"}, {"name": "10x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.681286, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 10, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "10x ********************************::navbar.badge-count", "param_count": null, "params": [], "start": **********.681729, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/navbar/badge-count.blade.php********************************::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::navbar.badge-count"}, {"name": "1x __components::42d71f4fe12493bd6829a606380e1435", "param_count": null, "params": [], "start": **********.684293, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/42d71f4fe12493bd6829a606380e1435.blade.php__components::42d71f4fe12493bd6829a606380e1435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F42d71f4fe12493bd6829a606380e1435.blade.php&line=1", "ajax": false, "filename": "42d71f4fe12493bd6829a606380e1435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42d71f4fe12493bd6829a606380e1435"}, {"name": "1x __components::1a65d1071503a2e05e73a706bfebac58", "param_count": null, "params": [], "start": **********.687823, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1a65d1071503a2e05e73a706bfebac58.blade.php__components::1a65d1071503a2e05e73a706bfebac58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1a65d1071503a2e05e73a706bfebac58.blade.php&line=1", "ajax": false, "filename": "1a65d1071503a2e05e73a706bfebac58.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1a65d1071503a2e05e73a706bfebac58"}, {"name": "1x __components::7e33c8bf87ccc47b22832a4eb415ca9d", "param_count": null, "params": [], "start": **********.690796, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7e33c8bf87ccc47b22832a4eb415ca9d.blade.php__components::7e33c8bf87ccc47b22832a4eb415ca9d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7e33c8bf87ccc47b22832a4eb415ca9d.blade.php&line=1", "ajax": false, "filename": "7e33c8bf87ccc47b22832a4eb415ca9d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e33c8bf87ccc47b22832a4eb415ca9d"}, {"name": "1x __components::452ccf81d5bbacec7ec062999540e293", "param_count": null, "params": [], "start": **********.692899, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/452ccf81d5bbacec7ec062999540e293.blade.php__components::452ccf81d5bbacec7ec062999540e293", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F452ccf81d5bbacec7ec062999540e293.blade.php&line=1", "ajax": false, "filename": "452ccf81d5bbacec7ec062999540e293.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::452ccf81d5bbacec7ec062999540e293"}, {"name": "1x __components::ee52ba22bc1262334c4146935fbed227", "param_count": null, "params": [], "start": **********.69681, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ee52ba22bc1262334c4146935fbed227.blade.php__components::ee52ba22bc1262334c4146935fbed227", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fee52ba22bc1262334c4146935fbed227.blade.php&line=1", "ajax": false, "filename": "ee52ba22bc1262334c4146935fbed227.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ee52ba22bc1262334c4146935fbed227"}, {"name": "1x __components::7bd0601fe0dc4645b269458a9ed5f144", "param_count": null, "params": [], "start": **********.699254, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7bd0601fe0dc4645b269458a9ed5f144.blade.php__components::7bd0601fe0dc4645b269458a9ed5f144", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7bd0601fe0dc4645b269458a9ed5f144.blade.php&line=1", "ajax": false, "filename": "7bd0601fe0dc4645b269458a9ed5f144.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7bd0601fe0dc4645b269458a9ed5f144"}, {"name": "1x __components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.70109, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e1ed162565cf31bd543a8427caaef1e"}, {"name": "1x __components::e3de92deb18074b63f585befac7d0965", "param_count": null, "params": [], "start": **********.704405, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e3de92deb18074b63f585befac7d0965.blade.php__components::e3de92deb18074b63f585befac7d0965", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe3de92deb18074b63f585befac7d0965.blade.php&line=1", "ajax": false, "filename": "e3de92deb18074b63f585befac7d0965.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3de92deb18074b63f585befac7d0965"}, {"name": "1x __components::69ab4d8cea5c292eefba375a85e9768a", "param_count": null, "params": [], "start": **********.706817, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/69ab4d8cea5c292eefba375a85e9768a.blade.php__components::69ab4d8cea5c292eefba375a85e9768a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F69ab4d8cea5c292eefba375a85e9768a.blade.php&line=1", "ajax": false, "filename": "69ab4d8cea5c292eefba375a85e9768a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69ab4d8cea5c292eefba375a85e9768a"}, {"name": "1x __components::64f291ea9b97f679266b5e47f5ae8464", "param_count": null, "params": [], "start": **********.708726, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/64f291ea9b97f679266b5e47f5ae8464.blade.php__components::64f291ea9b97f679266b5e47f5ae8464", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F64f291ea9b97f679266b5e47f5ae8464.blade.php&line=1", "ajax": false, "filename": "64f291ea9b97f679266b5e47f5ae8464.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::64f291ea9b97f679266b5e47f5ae8464"}, {"name": "2x __components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.710813, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9413e731e9bb6e320e018067130903e9"}, {"name": "1x __components::5d3f310865558ef9371c1fb4b468d742", "param_count": null, "params": [], "start": **********.712697, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5d3f310865558ef9371c1fb4b468d742.blade.php__components::5d3f310865558ef9371c1fb4b468d742", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5d3f310865558ef9371c1fb4b468d742.blade.php&line=1", "ajax": false, "filename": "5d3f310865558ef9371c1fb4b468d742.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5d3f310865558ef9371c1fb4b468d742"}, {"name": "2x __components::59b84d05fdb5c77612cfb696a101e213", "param_count": null, "params": [], "start": **********.714877, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/59b84d05fdb5c77612cfb696a101e213.blade.php__components::59b84d05fdb5c77612cfb696a101e213", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F59b84d05fdb5c77612cfb696a101e213.blade.php&line=1", "ajax": false, "filename": "59b84d05fdb5c77612cfb696a101e213.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::59b84d05fdb5c77612cfb696a101e213"}, {"name": "1x __components::2c23a08a8a248b19ed43094ec82cfb6b", "param_count": null, "params": [], "start": **********.71706, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2c23a08a8a248b19ed43094ec82cfb6b.blade.php__components::2c23a08a8a248b19ed43094ec82cfb6b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2c23a08a8a248b19ed43094ec82cfb6b.blade.php&line=1", "ajax": false, "filename": "2c23a08a8a248b19ed43094ec82cfb6b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2c23a08a8a248b19ed43094ec82cfb6b"}, {"name": "1x __components::1859ae2c66dc0538fd83aadc7f316844", "param_count": null, "params": [], "start": **********.722357, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1859ae2c66dc0538fd83aadc7f316844.blade.php__components::1859ae2c66dc0538fd83aadc7f316844", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1859ae2c66dc0538fd83aadc7f316844.blade.php&line=1", "ajax": false, "filename": "1859ae2c66dc0538fd83aadc7f316844.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1859ae2c66dc0538fd83aadc7f316844"}, {"name": "1x __components::ccda1407f97c36756562e2c01f09a7ab", "param_count": null, "params": [], "start": **********.724909, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ccda1407f97c36756562e2c01f09a7ab.blade.php__components::ccda1407f97c36756562e2c01f09a7ab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fccda1407f97c36756562e2c01f09a7ab.blade.php&line=1", "ajax": false, "filename": "ccda1407f97c36756562e2c01f09a7ab.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ccda1407f97c36756562e2c01f09a7ab"}, {"name": "1x __components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.727952, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::52309442cd989852b03aa65d96b55790"}, {"name": "1x __components::2cd2bd3a1a36cafc00007578f96771de", "param_count": null, "params": [], "start": **********.732548, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2cd2bd3a1a36cafc00007578f96771de.blade.php__components::2cd2bd3a1a36cafc00007578f96771de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2cd2bd3a1a36cafc00007578f96771de.blade.php&line=1", "ajax": false, "filename": "2cd2bd3a1a36cafc00007578f96771de.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2cd2bd3a1a36cafc00007578f96771de"}, {"name": "1x __components::24c50807435b9d6d46a8d65cd016c8b0", "param_count": null, "params": [], "start": **********.734605, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/24c50807435b9d6d46a8d65cd016c8b0.blade.php__components::24c50807435b9d6d46a8d65cd016c8b0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F24c50807435b9d6d46a8d65cd016c8b0.blade.php&line=1", "ajax": false, "filename": "24c50807435b9d6d46a8d65cd016c8b0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::24c50807435b9d6d46a8d65cd016c8b0"}, {"name": "1x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.736579, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "1x __components::2279ce135fc9e268c1dcef75eaca22a5", "param_count": null, "params": [], "start": **********.739579, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2279ce135fc9e268c1dcef75eaca22a5.blade.php__components::2279ce135fc9e268c1dcef75eaca22a5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2279ce135fc9e268c1dcef75eaca22a5.blade.php&line=1", "ajax": false, "filename": "2279ce135fc9e268c1dcef75eaca22a5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2279ce135fc9e268c1dcef75eaca22a5"}, {"name": "13x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.742087, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php&line=1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 13, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.755486, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php&line=1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::998e4178ecae37dacf7321232f455f64", "param_count": null, "params": [], "start": **********.758123, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/998e4178ecae37dacf7321232f455f64.blade.php__components::998e4178ecae37dacf7321232f455f64", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F998e4178ecae37dacf7321232f455f64.blade.php&line=1", "ajax": false, "filename": "998e4178ecae37dacf7321232f455f64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::998e4178ecae37dacf7321232f455f64"}, {"name": "1x __components::06e7cfabd119917d6efbd595a344e37b", "param_count": null, "params": [], "start": **********.760606, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/06e7cfabd119917d6efbd595a344e37b.blade.php__components::06e7cfabd119917d6efbd595a344e37b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F06e7cfabd119917d6efbd595a344e37b.blade.php&line=1", "ajax": false, "filename": "06e7cfabd119917d6efbd595a344e37b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::06e7cfabd119917d6efbd595a344e37b"}, {"name": "3x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.762739, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php&line=1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.767976, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php&line=1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.775868, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php&line=1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.78251, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php&line=1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.784181, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php&line=1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::e81d33e262b34e339fefa952b8ffa5f1", "param_count": null, "params": [], "start": **********.786587, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e81d33e262b34e339fefa952b8ffa5f1.blade.php__components::e81d33e262b34e339fefa952b8ffa5f1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe81d33e262b34e339fefa952b8ffa5f1.blade.php&line=1", "ajax": false, "filename": "e81d33e262b34e339fefa952b8ffa5f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81d33e262b34e339fefa952b8ffa5f1"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.789804, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php&line=1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::fda51db955ba828a198ee1c8d52b2003", "param_count": null, "params": [], "start": **********.792, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fda51db955ba828a198ee1c8d52b2003.blade.php__components::fda51db955ba828a198ee1c8d52b2003", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffda51db955ba828a198ee1c8d52b2003.blade.php&line=1", "ajax": false, "filename": "fda51db955ba828a198ee1c8d52b2003.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fda51db955ba828a198ee1c8d52b2003"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.799615, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.801748, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php&line=1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.803855, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php&line=1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.805888, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php&line=1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.808075, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php&line=1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.810535, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php&line=1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.816279, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php&line=1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.818127, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php&line=1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.819943, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php&line=1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.82175, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php&line=1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.823524, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php&line=1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.825264, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php&line=1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.827264, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php&line=1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.829245, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php&line=1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.830954, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php&line=1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.833672, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php&line=1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::1d5f951b8390a2ab38dcc695dab7b152", "param_count": null, "params": [], "start": **********.835251, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1d5f951b8390a2ab38dcc695dab7b152.blade.php__components::1d5f951b8390a2ab38dcc695dab7b152", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1d5f951b8390a2ab38dcc695dab7b152.blade.php&line=1", "ajax": false, "filename": "1d5f951b8390a2ab38dcc695dab7b152.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d5f951b8390a2ab38dcc695dab7b152"}, {"name": "1x __components::944e2ad490e8085f4039835ea5eefcbf", "param_count": null, "params": [], "start": **********.836912, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/944e2ad490e8085f4039835ea5eefcbf.blade.php__components::944e2ad490e8085f4039835ea5eefcbf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F944e2ad490e8085f4039835ea5eefcbf.blade.php&line=1", "ajax": false, "filename": "944e2ad490e8085f4039835ea5eefcbf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::944e2ad490e8085f4039835ea5eefcbf"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.839181, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php&line=1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.843113, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.845513, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php&line=1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.84617, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.846525, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.847416, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.84774, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.84854, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.848964, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": **********.850321, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php&line=1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x ********************************::form.index", "param_count": null, "params": [], "start": **********.852345, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/index.blade.php********************************::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.index"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.853124, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php&line=1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.853853, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php&line=1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.854573, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php&line=1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "1x ********************************::custom-template", "param_count": null, "params": [], "start": **********.855616, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/custom-template.blade.php********************************::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.856011, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x ********************************::loading", "param_count": null, "params": [], "start": **********.857122, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/loading.blade.php********************************::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::loading"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.86267, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x ********************************::debug-badge", "param_count": null, "params": [], "start": **********.035876, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/debug-badge.blade.php********************************::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::debug-badge"}, {"name": "1x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": **********.038365, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php&line=1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": **********.042161, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.042944, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.045127, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.047034, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.049438, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.050279, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00403, "accumulated_duration_str": "4.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.831178, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 18.859}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.83697, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.859, "width_percent": 7.692}, {"sql": "select `name`, `id` from `media_folders` where `parent_id` = 0 and `media_folders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Forms/MediaSettingForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Forms\\MediaSettingForm.php", "line": 36}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 576}], "start": **********.2065892, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "MediaSettingForm.php:36", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Forms/MediaSettingForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Forms\\MediaSettingForm.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FForms%2FMediaSettingForm.php&line=36", "ajax": false, "filename": "MediaSettingForm.php", "line": "36"}, "connection": "muhrak", "explain": null, "start_percent": 26.551, "width_percent": 50.124}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.64408, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1249", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1249", "ajax": false, "filename": "HookServiceProvider.php", "line": "1249"}, "connection": "muhrak", "explain": null, "start_percent": 76.675, "width_percent": 10.422}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.729542, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1293", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1293", "ajax": false, "filename": "HookServiceProvider.php", "line": "1293"}, "connection": "muhrak", "explain": null, "start_percent": 87.097, "width_percent": 12.903}]}, "models": {"data": {"Botble\\Media\\Models\\MediaFolder": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php&line=1", "ajax": false, "filename": "MediaFolder.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 20, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@edit", "uri": "GET admin/settings/media", "controller": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@edit<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Setting\\Http\\Controllers", "prefix": "admin/settings/media", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/setting/src/Http/Controllers/MediaSettingController.php:17-24</a>", "middleware": "web, core, auth", "duration": "4.89s", "peak_memory": "70MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1577246829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1577246829\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1633242808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1633242808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-11617256 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://muhrak.gc/admin/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijc3WG1lTEJJMWJHL0xqdjdVejVwRVE9PSIsInZhbHVlIjoiQzJwZjZESGdTeTdvK2NOUXdEbFNzbTN3VE0vMDRiUHoxNUdBaGw2S3dmdERSRUgzSGtEUm9RWGhUYWV1bS9BM0dKRmhRRlVnMEJRcEhERWxzL2FOaTlPOVlwcVI0djJ5RUlPdVMvNERmdXdmQlRYSW11UGRSTXdtbGxKS2pkZjQiLCJtYWMiOiJlZGJkYzhmNTdkMzc2NDY0Yzk5YWUwMThhMmM2MDI2YjY4M2VjMmUyNTY3NGRhOWNiY2VjNWQ5M2QzZjYwZTVmIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ik1qbDl3eHVtVFI0UWhZd2VQbUFVaXc9PSIsInZhbHVlIjoiei81cjZ6TDBBZFVJN1lkVCtKcXZJck5DbG91OFIvRVFYWjZMazI4VUw0TEg5ZlhhNVVVWm9rd1Urb0FvQWJMbzlYZFk5RllRMlNLSXRTM2daVG44QlNEU1hWTW9Xaml5dWtra3NZbDFPQmtEUnZvQXdiZkxxL0hubGFZWDEweDgiLCJtYWMiOiIzMGUzY2U0NDFhY2MyYTU3ZjFiN2UzMTRiODJjZjY1ODBlNmVhOTQyN2FmZjY2NzRlNmI1YTk4ODc3ZGFiMDM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11617256\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-990066445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990066445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:51:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1798857437 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://muhrak.gc/admin/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798857437\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@edit"}, "badge": null}}