{"__meta": {"id": "01JXN6QQHSD49WEHCWAK27AMK2", "datetime": "2025-06-13 17:52:23", "utime": **********.610612, "method": "GET", "uri": "/admin/media/list?view_type=tiles&filter=image&view_in=all_media&sort_by=created_at-desc&folder_id=17&search=icon&multiple=false&type=*&open_in=modal&load_more_file=false&paged=1&posts_per_page=40", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********42.628523, "end": **********.610641, "duration": 0.9821178913116455, "duration_str": "982ms", "measures": [{"label": "Booting", "start": ********42.628523, "relative_start": 0, "end": **********.458296, "relative_end": **********.458296, "duration": 0.**************, "duration_str": "830ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.458317, "relative_start": 0.****************, "end": **********.610644, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.480087, "relative_start": 0.****************, "end": **********.487509, "relative_end": **********.487509, "duration": 0.007421970367431641, "duration_str": "7.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::9a1da6c7f662474948fe63691d3a1543", "start": **********.539868, "relative_start": 0.****************, "end": **********.539868, "relative_end": **********.539868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.587512, "relative_start": 0.****************, "end": **********.587512, "relative_end": **********.587512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.599444, "relative_start": 0.9709208011627197, "end": **********.599444, "relative_end": **********.599444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.601968, "relative_start": 0.973444938659668, "end": **********.601968, "relative_end": **********.601968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.604496, "relative_start": 0.9759728908538818, "end": **********.604496, "relative_end": **********.604496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.60658, "relative_start": 0.9780569076538086, "end": **********.607247, "relative_end": **********.607247, "duration": 0.0006670951843261719, "duration_str": "667μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 53728616, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": **********.539838, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.587488, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.599418, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.601945, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.604473, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02117, "accumulated_duration_str": "21.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.5066311, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 2.362}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.515049, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.362, "width_percent": 2.362}, {"sql": "(select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` where `media_files`.`folder_id` = '17' and `media_files`.`name` LIKE '%icon%' and (`media_files`.`mime_type` in ('image/png', 'image/jpeg', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp', 'image/avif')) and `media_files`.`deleted_at` is null) union (select `media_folders`.`id` as `id`, `media_folders`.`name` as `name`, NULL as url, NULL as mime_type, NULL as size, NULL as alt, `media_folders`.`created_at` as `created_at`, `media_folders`.`updated_at` as `updated_at`, NULL as options, NULL as folder_id, NULL as visibility, 1 as is_folder, `media_folders`.`slug` as `slug`, `media_folders`.`parent_id` as `parent_id`, `media_folders`.`color` as `color` from `media_folders` where `parent_id` = '17' and `media_folders`.`name` LIKE '%icon%' and `media_folders`.`deleted_at` is null) order by `is_folder` asc, `created_at` desc limit 40 offset 0", "type": "query", "params": [], "bindings": ["17", "%icon%", "image/png", "image/jpeg", "image/gif", "image/bmp", "image/svg+xml", "image/webp", "image/avif", "17", "%icon%"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 225}, {"index": 17, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 18, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.541588, "duration": 0.01846, "duration_str": "18.46ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.724, "width_percent": 87.199}, {"sql": "select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` where `id` is null and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 19, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.562562, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 91.923, "width_percent": 1.748}, {"sql": "select * from `media_folders` where `media_folders`.`id` = '17' and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 259}, {"index": 19, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 228}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.5677001, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 93.67, "width_percent": 6.33}]}, "models": {"data": {"Botble\\Media\\Models\\MediaFile": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Media\\Models\\MediaFolder": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php&line=1", "ajax": false, "filename": "MediaFolder.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/media/list?filter=image&folder_id=17&load_more_file=false&multiple=false&ope...", "action_name": "media.list", "controller_action": "Botble\\Media\\Http\\Controllers\\MediaController@getList", "uri": "GET admin/media/list", "permission": "media.index", "controller": "Botble\\Media\\Http\\Controllers\\MediaController@getList<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaController.php&line=57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Media\\Http\\Controllers", "prefix": "admin/media", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaController.php&line=57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/media/src/Http/Controllers/MediaController.php:57-236</a>", "middleware": "web, core, auth", "duration": "974ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1365076050 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>view_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tiles</span>\"\n  \"<span class=sf-dump-key>filter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>view_in</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all_media</span>\"\n  \"<span class=sf-dump-key>sort_by</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n  \"<span class=sf-dump-key>folder_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"4 characters\">icon</span>\"\n  \"<span class=sf-dump-key>multiple</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str>*</span>\"\n  \"<span class=sf-dump-key>open_in</span>\" => \"<span class=sf-dump-str title=\"5 characters\">modal</span>\"\n  \"<span class=sf-dump-key>load_more_file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>paged</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>posts_per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365076050\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1265133176 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1265133176\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1829320403 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlNPcG80MEtZeGRJUFovQWQwWmFTTXc9PSIsInZhbHVlIjoiTU5sczRlZnM3YmZLcitZNjgzWURWREZaRXR4ak5ybExlMmdUTWxEYnhMWkpUNUVCTmdyQ1B6RXNKbWM5WnJsMkVXelVhYkc4dEEyT3dqYTh3NTMyQll4eHlFLzNIOFFGVzZrUGk1ZCtyeVY0aElYcWVPMUt2Tlc5ODVQc3Yrb2wiLCJtYWMiOiIzZWY1MTE2ZjM2NjhjODcwMWFmNTkwZmQyMTFkZDc4MjNlNWI3NjBkMDIxNTRmOThkN2FjNDUxYzYyY2RiNTAxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNPcG80MEtZeGRJUFovQWQwWmFTTXc9PSIsInZhbHVlIjoiTU5sczRlZnM3YmZLcitZNjgzWURWREZaRXR4ak5ybExlMmdUTWxEYnhMWkpUNUVCTmdyQ1B6RXNKbWM5WnJsMkVXelVhYkc4dEEyT3dqYTh3NTMyQll4eHlFLzNIOFFGVzZrUGk1ZCtyeVY0aElYcWVPMUt2Tlc5ODVQc3Yrb2wiLCJtYWMiOiIzZWY1MTE2ZjM2NjhjODcwMWFmNTkwZmQyMTFkZDc4MjNlNWI3NjBkMDIxNTRmOThkN2FjNDUxYzYyY2RiNTAxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlFrbEhENGJydWd3OGRQenpRZG5SYWc9PSIsInZhbHVlIjoiMlN1K1NlOUdWenF1U2xGd2tsRG13ZFNzVzNxeFNtOVpGa3Z3aHBPeUtISFVDVWVrVjhmS0JUeUswMEx6QVFscXI0NTlEekV2ZmVHc2JwTmlObjZpRDlMSUY0Q3crUUFqZUdNckNub29UMlg0dGJLRXBITmNUKzNaQmNlOFRtemEiLCJtYWMiOiIwOTk2YWM5ZTQzMGM0YjQ0NmI0ZDMzMjU2MzAyZjFmYWI2M2Y5NDFjMGQ1ZDI2ODgwOWMwY2QzNjgxMmFhYjEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829320403\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:52:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-355668447 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355668447\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/media/list?filter=image&folder_id=17&load_more_file=false&multiple=false&ope...", "action_name": "media.list", "controller_action": "Botble\\Media\\Http\\Controllers\\MediaController@getList"}, "badge": null}}