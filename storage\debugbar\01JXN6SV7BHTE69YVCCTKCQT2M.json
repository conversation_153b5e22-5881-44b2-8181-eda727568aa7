{"__meta": {"id": "01JXN6SV7BHTE69YVCCTKCQT2M", "datetime": "2025-06-13 17:53:32", "utime": **********.909226, "method": "PUT", "uri": "/admin/settings/media", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749837211.880162, "end": **********.909251, "duration": 1.0290889739990234, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1749837211.880162, "relative_start": 0, "end": **********.82875, "relative_end": **********.82875, "duration": 0.****************, "duration_str": "949ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.828767, "relative_start": 0.****************, "end": **********.909255, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "80.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.854288, "relative_start": 0.****************, "end": **********.861784, "relative_end": **********.861784, "duration": 0.007495880126953125, "duration_str": "7.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.905757, "relative_start": 1.**************, "end": **********.906192, "relative_end": **********.906192, "duration": 0.00043511390686035156, "duration_str": "435μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00074, "accumulated_duration_str": "740μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.875844, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 58.108}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.883482, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 58.108, "width_percent": 41.892}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media.update", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update", "uri": "PUT admin/settings/media", "permission": "settings.media", "controller": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Setting\\Http\\Controllers", "prefix": "admin/settings/media", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/setting/src/Http/Controllers/MediaSettingController.php:26-39</a>", "middleware": "web, core, auth, preventDemo", "duration": "1.03s", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1265585236 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1265585236\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-400296085 data-indent-pad=\"  \"><span class=sf-dump-note>array:76</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>media_driver</span>\" => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  \"<span class=sf-dump-key>media_aws_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_r2_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_do_spaces_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_cdn_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_do_spaces_cdn_custom_domain</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_wasabi_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_root</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_zone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_hostname</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_region</span>\" => \"<span class=sf-dump-str title=\"2 characters\">de</span>\"\n  \"<span class=sf-dump-key>media_backblaze_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_use_original_name_for_file_path</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_convert_file_name_to_uuid</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_keep_original_file_size_and_quality</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_turn_off_automatic_url_translation_into_latin</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>user_can_only_view_own_media</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_convert_image_to_webp</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_default_placeholder_image</span>\" => \"<span class=sf-dump-str title=\"34 characters\">icons-revamp/brand-placeholder.jpg</span>\"\n  \"<span class=sf-dump-key>max_upload_filesize</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_reduce_large_image_size</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_customize_upload_path</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_upload_path</span>\" => \"<span class=sf-dump-str title=\"7 characters\">storage</span>\"\n  \"<span class=sf-dump-key>media_image_max_width</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_image_max_height</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_chunk_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_chunk_size</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1048576</span>\"\n  \"<span class=sf-dump-key>media_max_file_size</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1048576</span>\"\n  \"<span class=sf-dump-key>media_watermark_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_folders_can_add_watermark</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str>5</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str>6</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str>7</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str>8</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>media_watermark_source</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_watermark_size</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_watermark_opacity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n  \"<span class=sf-dump-key>media_watermark_position</span>\" => \"<span class=sf-dump-str title=\"12 characters\">bottom-right</span>\"\n  \"<span class=sf-dump-key>media_watermark_position_x</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_watermark_position_y</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_image_processing_library</span>\" => \"<span class=sf-dump-str title=\"2 characters\">gd</span>\"\n  \"<span class=sf-dump-key>media_enable_thumbnail_sizes</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_sizes_thumb_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>media_sizes_thumb_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-thumb_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">237</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-thumb_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">162</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-logo_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-logo_height</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n  \"<span class=sf-dump-key>media_sizes_small_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">300</span>\"\n  \"<span class=sf-dump-key>media_sizes_small_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">300</span>\"\n  \"<span class=sf-dump-key>media_thumbnail_crop_position</span>\" => \"<span class=sf-dump-str title=\"6 characters\">center</span>\"\n  \"<span class=sf-dump-key>_js_validation</span>\" => \"<span class=sf-dump-str title=\"28 characters\">media_enable_thumbnail_sizes</span>\"\n  \"<span class=sf-dump-key>_js_validation_validate_all</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400296085\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1406159779 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"288 characters\">eyJpdiI6Ik5iT3lYYndpS3hjaEpja2JLeHYxSmc9PSIsInZhbHVlIjoiVWJCMm9PR3ZsMU9YaUUrS29obUF2cVZiMG1LSFdlZDR5WklKQm5uNzVHOTlwUEdPT0N5N1VEby9EWGhwVVh4ME8xRWM3dEhzb3pOR1pnc1JZdXhldWc9PSIsIm1hYyI6Ijk3YWVhZjY3NTExYzY4ODg0Y2Q4MWVhZmFhYTRkMmM1YWI0NjZlNDc4ZTYxMzFiNzc2ZTdlZTE3NDQ1MjMyNjQiLCJ0YWciOiIifQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2641</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InYyNVVieG83S01mTXpkbnNXcVdwY2c9PSIsInZhbHVlIjoiWmp0UmZnaXZ0ZjlOZnV2UVcxYUJYa25IdlhZU0hsbzFuK00wanQvMkMvWmxTREdwMUFLZm9ZZ2FxWDZIaXplVnFHbCtaM0dZNGpFVzFHejQxc1ByQzVWZWZubkxsNHRtS1VhNkk2RlhWUU13TXI5SytvM3VFdENIcEc0TUplZk4iLCJtYWMiOiIxZTExMTIyZjY4ZTMxZjU0ZjBjMjFiNGZmYTA5ZmZjYjIyYzI0MGUwZjBhMzUxMWJlN2IxMjQ2MzgxNzEwNDc5IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ims3NExCWUl0N1RVS2YwZDJVRjdRcHc9PSIsInZhbHVlIjoiWjVMWllFZ2Zlc0dKdXlOVlNldXRaZ0drcEFWT2JnelJsVk10cjNRV1ErSHgvV1d5RHRCUXhiVmtuVUhzZ1Y5clNyN0ZjZmtFbUpOdW5KQmd3VUdvc0I4TXJZMzF1Z29QMmxYMUxVdC82bTNCODhBeFNMSzNrOURiVFdLckRpc0MiLCJtYWMiOiI1M2FmM2M1ZTIwNjBiZjVmZWY5ZmZhMGI5MzIyNWYxZDIxY2VhNWVmMTFiN2EyM2U5ODQ0YWM3MDFiMjViNGZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406159779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:53:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2048898113 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048898113\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media.update", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update"}, "badge": null}}