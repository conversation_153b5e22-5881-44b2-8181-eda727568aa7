{"__meta": {"id": "01JXN6SXK8E5V05PYHFK5E91PT", "datetime": "2025-06-13 17:53:35", "utime": **********.337659, "method": "PUT", "uri": "/admin/settings/media", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749837212.970156, "end": **********.337672, "duration": 2.367516040802002, "duration_str": "2.37s", "measures": [{"label": "Booting", "start": 1749837212.970156, "relative_start": 0, "end": **********.698706, "relative_end": **********.698706, "duration": 0.****************, "duration_str": "729ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.698725, "relative_start": 0.****************, "end": **********.337673, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.720523, "relative_start": 0.****************, "end": **********.72653, "relative_end": **********.72653, "duration": 0.006006956100463867, "duration_str": "6.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.332942, "relative_start": 2.***************, "end": **********.335456, "relative_end": **********.335456, "duration": 0.002513885498046875, "duration_str": "2.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 389, "nb_statements": 389, "nb_visible_statements": 389, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.23401, "accumulated_duration_str": "1.23s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 289 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.7368789, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.033}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.742649, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.033, "width_percent": 0.031}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 17, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 18, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8388681, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "muhrak", "explain": null, "start_percent": 0.064, "width_percent": 0.109}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.843587, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 0.173, "width_percent": 0.301}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"blog\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"ecommerce\\\",\\\"faq\\\",\\\"location\\\",\\\"mollie\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paypal-payout\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"shippo\\\",\\\"simple-slider\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"stripe-connect\\\",\\\"translation\\\",\\\"marketplace\\\",\\\"magic\\\",\\\"fob-honeypot\\\"]', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"blog\",\"captcha\",\"contact\",\"cookie-consent\",\"ecommerce\",\"faq\",\"location\",\"mollie\",\"newsletter\",\"payment\",\"paypal\",\"paypal-payout\",\"paystack\",\"razorpay\",\"shippo\",\"simple-slider\",\"social-login\",\"sslcommerz\",\"stripe\",\"stripe-connect\",\"translation\",\"marketplace\",\"magic\",\"fob-honeypot\"]", "2025-06-13 17:53:33", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8490462, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 0.474, "width_percent": 0.292}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.85391, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 0.766, "width_percent": 0.288}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'enable_recaptcha_botble_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "enable_recaptcha_botble_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.858778, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 1.053, "width_percent": 0.272}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'api_layer_api_key'", "type": "query", "params": [], "bindings": ["", "2025-06-13 17:53:33", "api_layer_api_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8636181, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 1.326, "width_percent": 0.278}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.868189, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 1.604, "width_percent": 0.25}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.872334, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 1.853, "width_percent": 0.253}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-06-13 17:53:33", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8765912, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 2.106, "width_percent": 0.264}, {"sql": "update `settings` set `value` = 'name', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["name", "2025-06-13 17:53:33", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.881015, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 2.37, "width_percent": 0.266}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-06-13 17:53:33", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.885363, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 2.636, "width_percent": 0.278}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'simple_slider_using_assets'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "simple_slider_using_assets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8900008, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 2.914, "width_percent": 0.277}, {"sql": "update `settings` set `value` = '932321b7adf579f4d188075350462411', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["932321b7adf579f4d188075350462411", "2025-06-13 17:53:33", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.894795, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 3.191, "width_percent": 0.29}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.89961, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 3.481, "width_percent": 0.282}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.904182, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 3.763, "width_percent": 0.267}, {"sql": "update `settings` set `value` = 'muhrak', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["muhrak", "2025-06-13 17:53:33", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.90859, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 4.03, "width_percent": 0.281}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.913351, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 4.311, "width_percent": 0.284}, {"sql": "update `settings` set `value` = 'general/muhrak-logo.svg', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'admin_favicon'", "type": "query", "params": [], "bindings": ["general/muhrak-logo.svg", "2025-06-13 17:53:33", "admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.918113, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 4.596, "width_percent": 0.272}, {"sql": "update `settings` set `value` = 'general/muhrak-logo-white.svg', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'admin_logo'", "type": "query", "params": [], "bindings": ["general/muhrak-logo-white.svg", "2025-06-13 17:53:33", "admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9228761, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 4.868, "width_percent": 0.283}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'permalink-botble-blog-models-post'", "type": "query", "params": [], "bindings": ["blog", "2025-06-13 17:53:33", "permalink-botble-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.927795, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 5.151, "width_percent": 0.511}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'permalink-botble-blog-models-category'", "type": "query", "params": [], "bindings": ["blog", "2025-06-13 17:53:33", "permalink-botble-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.936168, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 5.662, "width_percent": 0.285}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-06-13 17:53:33", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.941845, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 5.947, "width_percent": 0.287}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-06-13 17:53:33", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.947428, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 6.234, "width_percent": 0.28}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-06-13 17:53:33", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.952262, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 6.515, "width_percent": 0.283}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'plugins_ecommerce_customer_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "plugins_ecommerce_customer_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9581518, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 6.797, "width_percent": 0.276}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'plugins_ecommerce_admin_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "plugins_ecommerce_admin_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.963079, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 7.074, "width_percent": 0.273}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_is_enabled_support_digital_products'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "ecommerce_is_enabled_support_digital_products"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.968255, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 7.347, "width_percent": 0.308}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_load_countries_states_cities_from_location_plugin'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "ecommerce_load_countries_states_cities_from_location_plugin"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.973842, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 7.655, "width_percent": 0.287}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'payment_bank_transfer_display_bank_info_at_the_checkout_success_page'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:33", "payment_bank_transfer_display_bank_info_at_the_checkout_success_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.978717, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 7.942, "width_percent": 0.271}, {"sql": "update `settings` set `value` = 'MF-2443-[%S]', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_product_sku_format'", "type": "query", "params": [], "bindings": ["MF-2443-[%S]", "2025-06-13 17:53:33", "ecommerce_product_sku_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.983193, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 8.212, "width_percent": 0.26}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_enable_product_specification'", "type": "query", "params": [], "bindings": ["0", "2025-06-13 17:53:33", "ecommerce_enable_product_specification"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9874828, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 8.472, "width_percent": 0.248}, {"sql": "update `settings` set `value` = 'Martfury', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_store_name'", "type": "query", "params": [], "bindings": ["Martfury", "2025-06-13 17:53:33", "ecommerce_store_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.991694, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 8.72, "width_percent": 0.214}, {"sql": "update `settings` set `value` = '**********', `settings`.`updated_at` = '2025-06-13 17:53:33' where `key` = 'ecommerce_store_phone'", "type": "query", "params": [], "bindings": ["**********", "2025-06-13 17:53:33", "ecommerce_store_phone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.996105, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 8.934, "width_percent": 0.286}, {"sql": "update `settings` set `value` = '502 New Street', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'ecommerce_store_address'", "type": "query", "params": [], "bindings": ["502 New Street", "2025-06-13 17:53:34", "ecommerce_store_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0017638, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 9.22, "width_percent": 0.288}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'ecommerce_store_state'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-06-13 17:53:34", "ecommerce_store_state"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.007431, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 9.509, "width_percent": 0.286}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'ecommerce_store_city'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-06-13 17:53:34", "ecommerce_store_city"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.013222, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 9.795, "width_percent": 0.29}, {"sql": "update `settings` set `value` = 'AU', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'ecommerce_store_country'", "type": "query", "params": [], "bindings": ["AU", "2025-06-13 17:53:34", "ecommerce_store_country"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.019154, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 10.085, "width_percent": 0.242}, {"sql": "update `settings` set `value` = 'Muhrak', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-site_title'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "2025-06-13 17:53:34", "theme-martfury-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0239952, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 10.327, "width_percent": 0.292}, {"sql": "update `settings` set `value` = 'MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-seo_description'", "type": "query", "params": [], "bindings": ["MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.", "2025-06-13 17:53:34", "theme-martfury-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.029681, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 10.619, "width_percent": 0.238}, {"sql": "update `settings` set `value` = '© %Y MartFury. All Rights Reserved.', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-copyright'", "type": "query", "params": [], "bindings": ["© %Y MartFury. All Rights Reserved.", "2025-06-13 17:53:34", "theme-martfury-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.034659, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 10.857, "width_percent": 0.234}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-06-13 17:53:34", "theme-martfury-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.039403, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 11.091, "width_percent": 0.284}, {"sql": "update `settings` set `value` = 'general/logo.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-logo'", "type": "query", "params": [], "bindings": ["general/logo.png", "2025-06-13 17:53:34", "theme-martfury-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.045157, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 11.375, "width_percent": 0.286}, {"sql": "update `settings` set `value` = 'Welcome to MartFury Online Shopping Store!', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-welcome_message'", "type": "query", "params": [], "bindings": ["Welcome to MartFury Online Shopping Store!", "2025-06-13 17:53:34", "theme-martfury-welcome_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.050596, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 11.661, "width_percent": 0.281}, {"sql": "update `settings` set `value` = '502 New Street, Brighton VIC, Australia', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-address'", "type": "query", "params": [], "bindings": ["502 New Street, Brighton VIC, Australia", "2025-06-13 17:53:34", "theme-martfury-address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.055815, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 11.942, "width_percent": 0.272}, {"sql": "update `settings` set `value` = '1800 97 97 69', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-hotline'", "type": "query", "params": [], "bindings": ["1800 97 97 69", "2025-06-13 17:53:34", "theme-martfury-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.060545, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 12.215, "width_percent": 0.216}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.065023, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 12.431, "width_percent": 0.216}, {"sql": "update `settings` set `value` = 'general/newsletter.jpg', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-newsletter_image'", "type": "query", "params": [], "bindings": ["general/newsletter.jpg", "2025-06-13 17:53:34", "theme-martfury-newsletter_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.069638, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 12.647, "width_percent": 0.22}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:34", "theme-martfury-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0743358, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 12.867, "width_percent": 0.233}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-06-13 17:53:34", "theme-martfury-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0784512, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 13.1, "width_percent": 0.267}, {"sql": "update `settings` set `value` = 'Your experience on this site will be improved by allowing cookies ', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-cookie_consent_message'", "type": "query", "params": [], "bindings": ["Your experience on this site will be improved by allowing cookies ", "2025-06-13 17:53:34", "theme-martfury-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.082931, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 13.367, "width_percent": 0.267}, {"sql": "update `settings` set `value` = '/cookie-policy', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["/cookie-policy", "2025-06-13 17:53:34", "theme-martfury-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0874472, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 13.634, "width_percent": 0.265}, {"sql": "update `settings` set `value` = 'Cookie Policy', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "2025-06-13 17:53:34", "theme-martfury-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0929809, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 13.899, "width_percent": 0.247}, {"sql": "update `settings` set `value` = '42', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-number_of_products_per_page'", "type": "query", "params": [], "bindings": ["42", "2025-06-13 17:53:34", "theme-martfury-number_of_products_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.098161, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 14.147, "width_percent": 0.215}, {"sql": "update `settings` set `value` = 'Shipping worldwide', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_1_title'", "type": "query", "params": [], "bindings": ["Shipping worldwide", "2025-06-13 17:53:34", "theme-martfury-product_feature_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.102954, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 14.361, "width_percent": 0.22}, {"sql": "update `settings` set `value` = 'icon-network', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_1_icon'", "type": "query", "params": [], "bindings": ["icon-network", "2025-06-13 17:53:34", "theme-martfury-product_feature_1_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1077058, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 14.582, "width_percent": 0.219}, {"sql": "update `settings` set `value` = 'Free 7-day return if eligible, so easy', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_2_title'", "type": "query", "params": [], "bindings": ["Free 7-day return if eligible, so easy", "2025-06-13 17:53:34", "theme-martfury-product_feature_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.111607, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 14.801, "width_percent": 0.315}, {"sql": "update `settings` set `value` = 'icon-3d-rotate', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_2_icon'", "type": "query", "params": [], "bindings": ["icon-3d-rotate", "2025-06-13 17:53:34", "theme-martfury-product_feature_2_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.116679, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 15.116, "width_percent": 0.219}, {"sql": "update `settings` set `value` = 'Supplier give bills for this product.', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_3_title'", "type": "query", "params": [], "bindings": ["Supplier give bills for this product.", "2025-06-13 17:53:34", "theme-martfury-product_feature_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.120569, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 15.335, "width_percent": 0.113}, {"sql": "update `settings` set `value` = 'icon-receipt', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_3_icon'", "type": "query", "params": [], "bindings": ["icon-receipt", "2025-06-13 17:53:34", "theme-martfury-product_feature_3_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.123631, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 15.447, "width_percent": 0.233}, {"sql": "update `settings` set `value` = 'Pay online or when receiving goods', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_4_title'", "type": "query", "params": [], "bindings": ["Pay online or when receiving goods", "2025-06-13 17:53:34", "theme-martfury-product_feature_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1282349, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 15.681, "width_percent": 0.205}, {"sql": "update `settings` set `value` = 'icon-credit-card', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-product_feature_4_icon'", "type": "query", "params": [], "bindings": ["icon-credit-card", "2025-06-13 17:53:34", "theme-martfury-product_feature_4_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.132529, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 15.886, "width_percent": 0.216}, {"sql": "update `settings` set `value` = 'Contact Directly', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_1_title'", "type": "query", "params": [], "bindings": ["Contact Directly", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.136408, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.101, "width_percent": 0.211}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_1_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_1_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.140986, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.312, "width_percent": 0.142}, {"sql": "update `settings` set `value` = '(+004) 912-3548-07', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_1_details'", "type": "query", "params": [], "bindings": ["(+004) 912-3548-07", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_1_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.144189, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.454, "width_percent": 0.215}, {"sql": "update `settings` set `value` = 'Headquarters', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_2_title'", "type": "query", "params": [], "bindings": ["Headquarters", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.148072, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.668, "width_percent": 0.116}, {"sql": "update `settings` set `value` = '17 Queen St, South bank, Melbourne 10560, Australia', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_2_subtitle'", "type": "query", "params": [], "bindings": ["17 Queen St, South bank, Melbourne 10560, Australia", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_2_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.15062, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.784, "width_percent": 0.103}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_2_details'", "type": "query", "params": [], "bindings": ["", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_2_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.153009, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 16.887, "width_percent": 0.204}, {"sql": "update `settings` set `value` = 'Work With Us', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_3_title'", "type": "query", "params": [], "bindings": ["Work With Us", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1566951, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.091, "width_percent": 0.223}, {"sql": "update `settings` set `value` = 'Send your CV to our email:', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_3_subtitle'", "type": "query", "params": [], "bindings": ["Send your CV to our email:", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_3_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1606781, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.314, "width_percent": 0.207}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_3_details'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_3_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1645472, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.522, "width_percent": 0.207}, {"sql": "update `settings` set `value` = 'Customer Service', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_4_title'", "type": "query", "params": [], "bindings": ["Customer Service", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.168282, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.728, "width_percent": 0.236}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_4_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_4_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1723928, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.964, "width_percent": 0.203}, {"sql": "update `settings` set `value` = '(800) 843-2446', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_4_details'", "type": "query", "params": [], "bindings": ["(800) 843-2446", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_4_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1762168, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.167, "width_percent": 0.216}, {"sql": "update `settings` set `value` = 'Media Relations', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_5_title'", "type": "query", "params": [], "bindings": ["Media Relations", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_5_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1807702, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.383, "width_percent": 0.108}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_5_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_5_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.18318, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.491, "width_percent": 0.098}, {"sql": "update `settings` set `value` = '(801) 947-3564', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_5_details'", "type": "query", "params": [], "bindings": ["(801) 947-3564", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_5_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.185468, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.589, "width_percent": 0.098}, {"sql": "update `settings` set `value` = 'Vendor Support', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_6_title'", "type": "query", "params": [], "bindings": ["Vendor Support", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_6_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.188612, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.687, "width_percent": 0.106}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_6_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_6_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.191824, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.793, "width_percent": 0.124}, {"sql": "update `settings` set `value` = '(801) 947-3100', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-contact_info_box_6_details'", "type": "query", "params": [], "bindings": ["(801) 947-3100", "2025-06-13 17:53:34", "theme-martfury-contact_info_box_6_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.19545, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 18.917, "width_percent": 0.212}, {"sql": "update `settings` set `value` = '7', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-number_of_cross_sale_product'", "type": "query", "params": [], "bindings": ["7", "2025-06-13 17:53:34", "theme-martfury-number_of_cross_sale_product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.199893, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.129, "width_percent": 0.109}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-logo_in_the_checkout_page'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-13 17:53:34", "theme-martfury-logo_in_the_checkout_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.202393, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.238, "width_percent": 0.109}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-logo_in_invoices'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-13 17:53:34", "theme-martfury-logo_in_invoices"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.204845, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.347, "width_percent": 0.106}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-logo_vendor_dashboard'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-13 17:53:34", "theme-martfury-logo_vendor_dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2080338, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.453, "width_percent": 0.106}, {"sql": "update `settings` set `value` = 'Work Sans', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-primary_font'", "type": "query", "params": [], "bindings": ["Work Sans", "2025-06-13 17:53:34", "theme-martfury-primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.210583, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.559, "width_percent": 0.103}, {"sql": "update `settings` set `value` = '[\\\"general\\\\/payment-method-1.jpg\\\",\\\"general\\\\/payment-method-2.jpg\\\",\\\"general\\\\/payment-method-3.jpg\\\",\\\"general\\\\/payment-method-4.jpg\\\",\\\"general\\\\/payment-method-5.jpg\\\"]', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-payment_methods'", "type": "query", "params": [], "bindings": ["[\"general\\/payment-method-1.jpg\",\"general\\/payment-method-2.jpg\",\"general\\/payment-method-3.jpg\",\"general\\/payment-method-4.jpg\",\"general\\/payment-method-5.jpg\"]", "2025-06-13 17:53:34", "theme-martfury-payment_methods"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.212948, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.662, "width_percent": 0.1}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.facebook.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"X (Twitter)\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/x.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"YouTube\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-youtube\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.youtube.com\\\"}],[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Instagram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/www.linkedin.com\\\"}]]', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.facebook.com\"}],[{\"key\":\"name\",\"value\":\"<PERSON> (Twitter)\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"},{\"key\":\"url\",\"value\":\"https:\\/\\/x.com\"}],[{\"key\":\"name\",\"value\":\"YouTube\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-youtube\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.youtube.com\"}],[{\"key\":\"name\",\"value\":\"Instagram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"},{\"key\":\"url\",\"value\":\"https:\\/\\/www.linkedin.com\"}]]", "2025-06-13 17:53:34", "theme-martfury-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.215266, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.762, "width_percent": 0.109}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-06-13 17:53:34", "theme-martfury-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.217706, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.871, "width_percent": 0.11}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-lazy_load_images'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:34", "theme-martfury-lazy_load_images"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.220968, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 19.981, "width_percent": 0.123}, {"sql": "update `settings` set `value` = 'general/placeholder.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-lazy_load_placeholder_image'", "type": "query", "params": [], "bindings": ["general/placeholder.png", "2025-06-13 17:53:34", "theme-martfury-lazy_load_placeholder_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.223799, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.104, "width_percent": 0.194}, {"sql": "update `settings` set `value` = 'rgb(1, 36, 166)', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-primary_color'", "type": "query", "params": [], "bindings": ["rgb(1, 36, 166)", "2025-06-13 17:53:34", "theme-martfury-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.22738, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.299, "width_percent": 0.104}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-admin_logo'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-06-13 17:53:34", "theme-martfury-admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.230664, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.403, "width_percent": 0.108}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-admin_favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-06-13 17:53:34", "theme-martfury-admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.234022, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.51, "width_percent": 0.119}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-06-13 17:53:34", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2368271, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.629, "width_percent": 0.122}, {"sql": "update `settings` set `value` = '#222222', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-secondary_color'", "type": "query", "params": [], "bindings": ["#222222", "2025-06-13 17:53:34", "theme-martfury-secondary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2394931, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.752, "width_percent": 0.109}, {"sql": "update `settings` set `value` = '#000', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-header_text_color'", "type": "query", "params": [], "bindings": ["#000", "2025-06-13 17:53:34", "theme-martfury-header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.242194, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.86, "width_percent": 0.126}, {"sql": "update `settings` set `value` = '#fff', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-header_text_hover_color'", "type": "query", "params": [], "bindings": ["#fff", "2025-06-13 17:53:34", "theme-martfury-header_text_hover_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.245959, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.987, "width_percent": 0.127}, {"sql": "update `settings` set `value` = '#000', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-header_text_accent_color'", "type": "query", "params": [], "bindings": ["#000", "2025-06-13 17:53:34", "theme-martfury-header_text_accent_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.249728, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 21.114, "width_percent": 0.142}, {"sql": "update `settings` set `value` = '#000', `settings`.`updated_at` = '2025-06-13 17:53:34' where `key` = 'theme-martfury-header_button_background_color'", "type": "query", "params": [], "bindings": ["#000", "2025-06-13 17:53:34", "theme-martfury-header_button_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/Concerns/InteractsWithSettings.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\Concerns\\InteractsWithSettings.php", "line": 24}, {"index": 16, "namespace": null, "name": "platform/core/setting/src/Http/Controllers/MediaSettingController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Http\\Controllers\\MediaSettingController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2529118, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 21.256, "width_percent": 0.119}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256449, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.375, "width_percent": 0.124}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.25832, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.499, "width_percent": 0.122}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2600982, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.621, "width_percent": 0.107}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.261641, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.728, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2629728, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.818, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.264297, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.909, "width_percent": 0.099}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.265731, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.008, "width_percent": 0.098}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2671618, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.106, "width_percent": 0.105}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.268712, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.211, "width_percent": 0.1}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.270272, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.311, "width_percent": 0.101}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.271775, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.412, "width_percent": 0.1}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2732942, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.513, "width_percent": 0.107}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2751281, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.62, "width_percent": 0.127}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.277215, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.747, "width_percent": 0.126}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2790852, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.873, "width_percent": 0.108}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.280671, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.98, "width_percent": 0.1}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.282195, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.081, "width_percent": 0.11}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.283999, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.191, "width_percent": 0.113}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.285669, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.304, "width_percent": 0.109}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287256, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.413, "width_percent": 0.103}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.288774, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.516, "width_percent": 0.104}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2903159, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.62, "width_percent": 0.105}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.291884, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.724, "width_percent": 0.114}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293582, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.839, "width_percent": 0.128}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.295394, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.967, "width_percent": 0.103}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.296895, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.069, "width_percent": 0.1}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2984128, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.17, "width_percent": 0.12}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.300345, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.29, "width_percent": 0.118}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.302109, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.408, "width_percent": 0.113}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.30378, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.521, "width_percent": 0.113}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.305448, "duration": 0.006690000000000001, "duration_str": "6.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.634, "width_percent": 0.542}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.312474, "duration": 0.006719999999999999, "duration_str": "6.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.176, "width_percent": 0.545}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3195639, "duration": 0.00674, "duration_str": "6.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.721, "width_percent": 0.546}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.326739, "duration": 0.0067599999999999995, "duration_str": "6.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.267, "width_percent": 0.548}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.333875, "duration": 0.006889999999999999, "duration_str": "6.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.815, "width_percent": 0.558}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.341294, "duration": 0.00683, "duration_str": "6.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.373, "width_percent": 0.553}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.348643, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.927, "width_percent": 0.553}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3559968, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.48, "width_percent": 0.586}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.363702, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.065, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.366678, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.276, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3693979, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.474, "width_percent": 0.218}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372477, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.692, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3755958, "duration": 0.0069299999999999995, "duration_str": "6.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.903, "width_percent": 0.562}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.383008, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.464, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386865, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.74, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390094, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.959, "width_percent": 0.221}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393324, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.18, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3964748, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.399, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3992798, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.604, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4021869, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.81, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405349, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.026, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4083521, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.242, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411255, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.453, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414043, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.659, "width_percent": 0.2}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416791, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.859, "width_percent": 0.233}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42006, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.091, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422903, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.296, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426016, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.509, "width_percent": 0.294}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430077, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.803, "width_percent": 0.256}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433495, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.059, "width_percent": 0.252}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4368799, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.311, "width_percent": 0.257}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.440363, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.568, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443974, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.836, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447738, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.115, "width_percent": 0.261}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451385, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.376, "width_percent": 0.261}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.455054, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.637, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458677, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.903, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.462321, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.174, "width_percent": 0.259}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4659522, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.433, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.46965, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.697, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473266, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.965, "width_percent": 0.272}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.477099, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.237, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.48094, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.51, "width_percent": 0.266}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.484539, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.776, "width_percent": 0.274}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.488242, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.05, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.491949, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.321, "width_percent": 0.27}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4957678, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.591, "width_percent": 0.259}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.499282, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.851, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.503112, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.121, "width_percent": 0.266}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5068178, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.387, "width_percent": 0.262}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5104039, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.649, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.51414, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.92, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5182178, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.206, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.522336, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.496, "width_percent": 0.278}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.526217, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.774, "width_percent": 0.301}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5302181, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.075, "width_percent": 0.264}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.533755, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.339, "width_percent": 0.259}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537242, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.599, "width_percent": 0.261}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.540881, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.859, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.544549, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.133, "width_percent": 0.274}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.548194, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.406, "width_percent": 0.257}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.551645, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.663, "width_percent": 0.257}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555212, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.92, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.558794, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.184, "width_percent": 0.283}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.562569, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.466, "width_percent": 0.258}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565994, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.724, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569646, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.997, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.573191, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.26, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.577461, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.57, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.581386, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.858, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.585522, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.154, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.589097, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.417, "width_percent": 0.272}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.592844, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.689, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.596699, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.965, "width_percent": 0.27}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.600533, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.235, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6044261, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.51, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608218, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.789, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.612054, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.075, "width_percent": 0.278}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6157901, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.353, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6194808, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.621, "width_percent": 0.262}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.622997, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.883, "width_percent": 0.269}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6266348, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.152, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630312, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.428, "width_percent": 0.261}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.633775, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.689, "width_percent": 0.256}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637232, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.945, "width_percent": 0.269}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6409009, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.214, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644683, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.491, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.648616, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.766, "width_percent": 0.278}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.652576, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.044, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656429, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.324, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660444, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.607, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664313, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.892, "width_percent": 0.292}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6682632, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.184, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672066, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.462, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675843, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.739, "width_percent": 0.283}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679863, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.021, "width_percent": 0.283}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683851, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.304, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68773, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.59, "width_percent": 0.278}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6915, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.868, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695411, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.156, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699167, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.424, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.703047, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.71, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706861, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.978, "width_percent": 0.292}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710846, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.27, "width_percent": 0.275}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714724, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.545, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718633, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.831, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7226732, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.115, "width_percent": 0.294}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726649, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.41, "width_percent": 0.331}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7310772, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.741, "width_percent": 0.335}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7356489, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.076, "width_percent": 0.26}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739262, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.336, "width_percent": 0.254}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742827, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.589, "width_percent": 0.281}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746674, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.871, "width_percent": 0.292}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7506628, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.162, "width_percent": 0.289}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7547688, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.452, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7587218, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.728, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762844, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.019, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7665768, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.299, "width_percent": 0.277}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7703311, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.576, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7743402, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.871, "width_percent": 0.282}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778262, "duration": 0.0053, "duration_str": "5.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.153, "width_percent": 0.429}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7839491, "duration": 0.00592, "duration_str": "5.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.582, "width_percent": 0.48}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7903469, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.062, "width_percent": 0.318}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.794731, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.38, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7989771, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.683, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803387, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.992, "width_percent": 0.299}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807658, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.291, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811814, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.582, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.815979, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.879, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8199658, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.166, "width_percent": 0.301}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.824125, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.467, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8279989, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.753, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831776, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.025, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835566, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.298, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839588, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.585, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84367, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.872, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.847682, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.16, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851469, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.439, "width_percent": 0.338}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855983, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.777, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8596978, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.044, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863416, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.32, "width_percent": 0.256}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.866867, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.576, "width_percent": 0.258}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870427, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.834, "width_percent": 0.262}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.873992, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.096, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.877772, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.363, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.881437, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.627, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8849602, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.89, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888746, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.17, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892491, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.433, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.896047, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.696, "width_percent": 0.269}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.899666, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.965, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903281, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.233, "width_percent": 0.27}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9069061, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.503, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.910691, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.782, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91424, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.05, "width_percent": 0.26}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.917674, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.311, "width_percent": 0.259}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.921219, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.569, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9251878, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.848, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929056, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.124, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932689, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.387, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9364688, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.655, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940573, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.943, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944691, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.223, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9485848, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.511, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952198, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.787, "width_percent": 0.274}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.95581, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.061, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959482, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.34, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963004, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.606, "width_percent": 0.268}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966537, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.875, "width_percent": 0.265}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970029, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.14, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973581, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.407, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97722, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.678, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980827, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.952, "width_percent": 0.263}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9843059, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.215, "width_percent": 0.261}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.987747, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.476, "width_percent": 0.267}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.991288, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.743, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.994944, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.015, "width_percent": 0.278}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.998718, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.293, "width_percent": 0.302}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.00284, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.595, "width_percent": 0.346}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.007442, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.941, "width_percent": 0.294}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011359, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.235, "width_percent": 0.305}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.015456, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.54, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.019386, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.837, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023152, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.122, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.026897, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.402, "width_percent": 0.281}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0306, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.683, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0342689, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.962, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.038034, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.25, "width_percent": 0.283}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0418181, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.533, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04584, "duration": 0.00566, "duration_str": "5.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.83, "width_percent": 0.459}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0518239, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.289, "width_percent": 0.318}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.056141, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.606, "width_percent": 0.304}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.060257, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.91, "width_percent": 0.304}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.064377, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.214, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0685198, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.52, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.072679, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.826, "width_percent": 0.31}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077042, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.136, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.081014, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.432, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08477, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.72, "width_percent": 0.274}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.088412, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.994, "width_percent": 0.274}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0920641, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.268, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0957382, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.546, "width_percent": 0.281}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.099426, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.828, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.103231, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.114, "width_percent": 0.29}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.107141, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.405, "width_percent": 0.286}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.110935, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.691, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.114684, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.976, "width_percent": 0.282}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1183841, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.258, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1221411, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.538, "width_percent": 0.282}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.125973, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.82, "width_percent": 0.331}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.13044, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.152, "width_percent": 0.307}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.134588, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.459, "width_percent": 0.301}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.138666, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.76, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.142772, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.063, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.146612, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.351, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.15057, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.642, "width_percent": 0.305}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.154648, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.947, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.158563, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.242, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.162478, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.529, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.166464, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.826, "width_percent": 0.293}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.170342, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.119, "width_percent": 0.275}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1739728, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.394, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.177922, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.682, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.181753, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.966, "width_percent": 0.288}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.185532, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.254, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1893, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.533, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193283, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.829, "width_percent": 0.285}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197074, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.114, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2011042, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.412, "width_percent": 0.309}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205283, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.72, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2093852, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.026, "width_percent": 0.28}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213109, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.306, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2168858, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.583, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2210329, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.874, "width_percent": 0.302}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.225271, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.176, "width_percent": 0.317}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229555, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.493, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2337961, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.806, "width_percent": 0.452}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.239748, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.258, "width_percent": 0.308}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.243907, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.566, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247938, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.863, "width_percent": 0.291}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2518332, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.154, "width_percent": 0.306}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2559521, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.46, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.25989, "duration": 0.0069500000000000004, "duration_str": "6.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.758, "width_percent": 0.563}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.267204, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.321, "width_percent": 0.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2713158, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.624, "width_percent": 0.305}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.275443, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.929, "width_percent": 0.313}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279631, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.242, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.283378, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.525, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28701, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.798, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.291061, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.095, "width_percent": 0.289}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294954, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.384, "width_percent": 0.293}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298782, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.677, "width_percent": 0.271}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.302387, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.947, "width_percent": 0.296}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.306358, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.243, "width_percent": 0.293}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3102078, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.536, "width_percent": 0.287}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.313988, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.823, "width_percent": 0.276}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.317617, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.099, "width_percent": 0.282}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.32136, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.381, "width_percent": 0.293}, {"sql": "select count(*) as aggregate from `media_files` where `media_files`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.328628, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.674, "width_percent": 0.326}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media.update", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update", "uri": "PUT admin/settings/media", "permission": "settings.media", "controller": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Setting\\Http\\Controllers", "prefix": "admin/settings/media", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FHttp%2FControllers%2FMediaSettingController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/setting/src/Http/Controllers/MediaSettingController.php:26-39</a>", "middleware": "web, core, auth, preventDemo", "duration": "2.38s", "peak_memory": "52MB", "response": "Redirect to https://muhrak.gc/admin/settings/media", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1311535006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1311535006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1922609959 data-indent-pad=\"  \"><span class=sf-dump-note>array:74</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>media_driver</span>\" => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  \"<span class=sf-dump-key>media_aws_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_aws_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_r2_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_r2_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_do_spaces_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_cdn_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_do_spaces_cdn_custom_domain</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_do_spaces_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_wasabi_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_wasabi_root</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_zone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_hostname</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_bunnycdn_region</span>\" => \"<span class=sf-dump-str title=\"2 characters\">de</span>\"\n  \"<span class=sf-dump-key>media_backblaze_access_key_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_secret_key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_default_region</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_bucket</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_endpoint</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_backblaze_use_path_style_endpoint</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_use_original_name_for_file_path</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_convert_file_name_to_uuid</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_keep_original_file_size_and_quality</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_turn_off_automatic_url_translation_into_latin</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>user_can_only_view_own_media</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_convert_image_to_webp</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_default_placeholder_image</span>\" => \"<span class=sf-dump-str title=\"34 characters\">icons-revamp/brand-placeholder.jpg</span>\"\n  \"<span class=sf-dump-key>max_upload_filesize</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_reduce_large_image_size</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_customize_upload_path</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_upload_path</span>\" => \"<span class=sf-dump-str title=\"7 characters\">storage</span>\"\n  \"<span class=sf-dump-key>media_image_max_width</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_image_max_height</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_chunk_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_chunk_size</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1048576</span>\"\n  \"<span class=sf-dump-key>media_max_file_size</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1048576</span>\"\n  \"<span class=sf-dump-key>media_watermark_enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>media_folders_can_add_watermark</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str>5</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str>6</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str>7</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str>8</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>media_watermark_source</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>media_watermark_size</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_watermark_opacity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n  \"<span class=sf-dump-key>media_watermark_position</span>\" => \"<span class=sf-dump-str title=\"12 characters\">bottom-right</span>\"\n  \"<span class=sf-dump-key>media_watermark_position_x</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_watermark_position_y</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>media_image_processing_library</span>\" => \"<span class=sf-dump-str title=\"2 characters\">gd</span>\"\n  \"<span class=sf-dump-key>media_enable_thumbnail_sizes</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>media_sizes_thumb_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>media_sizes_thumb_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-thumb_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">237</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-thumb_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">162</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-logo_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>media_sizes_store-logo_height</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n  \"<span class=sf-dump-key>media_sizes_small_width</span>\" => \"<span class=sf-dump-str title=\"3 characters\">300</span>\"\n  \"<span class=sf-dump-key>media_sizes_small_height</span>\" => \"<span class=sf-dump-str title=\"3 characters\">300</span>\"\n  \"<span class=sf-dump-key>media_thumbnail_crop_position</span>\" => \"<span class=sf-dump-str title=\"6 characters\">center</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922609959\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1700743188 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2563</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1mTUJ3cGJEK0FpTnFvVG1iRy9nclE9PSIsInZhbHVlIjoiOGh6bi82OEwwYjFmaHlGRjlTN29PTTlscjJFaWRBTGg0T2VoR1c3REVIVDFYOHh2VmdDS1czQmw2UTdwbk1nUHNjUlVHTlBtL0tDNlpET2hrT2U2a0g5TVZ6YUJNbUFicGVocnFWcVZLeHI5TWdxZTQ4MVRET0tYUEtjM2VTSFYiLCJtYWMiOiJkZmZjODQ2OWFiYzQzYzRiZDdjZDRmN2Y0ZmUzYTI2OTE2MTUyY2ZkZTgzNjdlZGJiYmY4ZGZhZGE2NWRkZjFlIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IitsaFZTMzNGTXNrTzdPSlBqNlZqekE9PSIsInZhbHVlIjoiNG5IZXp4VncyNEd6Q3NQWnFTQ1lhNUhzbUduaXB5VWxlNCs3eDJMaEc4RzQ4VFBKeHlYWEZUUzdDTG5oN3RSRlJ4NEprbzFiYnV1cGFpMjcrNDczNks0UjEyQUQzeENKM2VKbXhDcURtYUtBaitadDNyL2JGK3NoN0lOMytleDciLCJtYWMiOiIzYTcwNWUxNDc4ZDNhNmJiM2Q4OWM5ZDViMGI4NzhmZjRiMjkyNzhhOTEwMGI5NjhlOGFiZjIxN2Y3OGUxYjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700743188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-522780101 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522780101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-475620244 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:53:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475620244\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2043007717 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://muhrak.gc/admin/settings/media</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043007717\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/settings/media", "action_name": "settings.media.update", "controller_action": "Botble\\Setting\\Http\\Controllers\\MediaSettingController@update"}, "badge": "302 Found"}}