{"__meta": {"id": "01JXN9JCHYYPKCP5F26M5W8CJM", "datetime": "2025-06-13 18:41:54", "utime": **********.239184, "method": "POST", "uri": "/admin/tools/data-synchronize/import/products/validate", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.147006, "end": **********.239198, "duration": 23.09219193458557, "duration_str": "23.09s", "measures": [{"label": "Booting", "start": **********.147006, "relative_start": 0, "end": **********.934229, "relative_end": **********.934229, "duration": 0.****************, "duration_str": "787ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.934316, "relative_start": 0.****************, "end": **********.239199, "relative_end": 9.5367431640625e-07, "duration": 22.***************, "duration_str": "22.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.954878, "relative_start": 0.****************, "end": **********.967553, "relative_end": **********.967553, "duration": 0.012674808502197266, "duration_str": "12.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.234577, "relative_start": 23.***************, "end": **********.236205, "relative_end": **********.236205, "duration": 0.0016281604766845703, "duration_str": "1.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "66MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 99, "nb_statements": 99, "nb_visible_statements": 99, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.049800000000000004, "accumulated_duration_str": "49.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.9792411, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 1.024}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.986661, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.024, "width_percent": 0.843}, {"sql": "select * from `ec_product_attribute_sets`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": 1749840092.00866, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.867, "width_percent": 0.924}, {"sql": "select * from `ec_product_attributes` where `ec_product_attributes`.`attribute_set_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": 1749840092.0132961, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.791, "width_percent": 0.964}, {"sql": "select * from `ec_taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": 1749840092.0168319, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.755, "width_percent": 1.104}, {"sql": "select `lang_code` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 94}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": 1749840092.0191178, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.859, "width_percent": 0.803}, {"sql": "select * from `ec_product_categories` where `name` = 'Coating' limit 1", "type": "query", "params": [], "bindings": ["Coating"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840093.795964, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.663, "width_percent": 1.145}, {"sql": "select * from `ec_product_categories` where `name` = 'machine' limit 1", "type": "query", "params": [], "bindings": ["machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840093.832263, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.807, "width_percent": 1.024}, {"sql": "select * from `ec_product_categories` where `name` = 'heater' limit 1", "type": "query", "params": [], "bindings": ["heater"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840093.881901, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.831, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'cooler' limit 1", "type": "query", "params": [], "bindings": ["cooler"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840093.890774, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.876, "width_percent": 0.904}, {"sql": "select * from `ec_product_categories` where `name` = 'Filters' limit 1", "type": "query", "params": [], "bindings": ["Filters"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840093.982522, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.779, "width_percent": 1.084}, {"sql": "select * from `ec_product_categories` where `name` = 'Conveyor' limit 1", "type": "query", "params": [], "bindings": ["Conveyor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.039525, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.863, "width_percent": 1.024}, {"sql": "select * from `ec_product_categories` where `name` = 'Buffer' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.1436641, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.888, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Valve' limit 1", "type": "query", "params": [], "bindings": ["Valve"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.15224, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.871, "width_percent": 0.803}, {"sql": "select * from `ec_product_categories` where `name` = 'Pumps' limit 1", "type": "query", "params": [], "bindings": ["Pumps"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.161205, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.675, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'Motors' limit 1", "type": "query", "params": [], "bindings": ["Motors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.169561, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.739, "width_percent": 0.843}, {"sql": "select * from `ec_product_categories` where `name` = 'Washing Machine Component' limit 1", "type": "query", "params": [], "bindings": ["Washing Machine Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.185338, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.582, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Sealing Component' limit 1", "type": "query", "params": [], "bindings": ["Industrial Sealing Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.202047, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.526, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'Automotive Suspension Component' limit 1", "type": "query", "params": [], "bindings": ["Automotive Suspension Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.211442, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.57, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'Automobile Components' limit 1", "type": "query", "params": [], "bindings": ["Automobile Components"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.220323, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.635, "width_percent": 0.884}, {"sql": "select * from `ec_product_categories` where `name` = 'Semiconductor' limit 1", "type": "query", "params": [], "bindings": ["Semiconductor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.262389, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.518, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Blades' limit 1", "type": "query", "params": [], "bindings": ["Blades"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.4104571, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.522, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'chain' limit 1", "type": "query", "params": [], "bindings": ["chain"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.572907, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.506, "width_percent": 1.185}, {"sql": "select * from `ec_product_categories` where `name` = 'Automotive Electrical Component' limit 1", "type": "query", "params": [], "bindings": ["Automotive Electrical Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.6207001, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 22.691, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial and Commercial HVAC Component' limit 1", "type": "query", "params": [], "bindings": ["Industrial and Commercial HVAC Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.63911, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 23.635, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Corrosion Protection Coating System' limit 1", "type": "query", "params": [], "bindings": ["Corrosion Protection Coating System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.655714, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 24.578, "width_percent": 1.506}, {"sql": "select * from `ec_product_categories` where `name` = 'Precision Metal Components' limit 1", "type": "query", "params": [], "bindings": ["Precision Metal Components"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.6672041, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 26.084, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Pump' limit 1", "type": "query", "params": [], "bindings": ["Industrial Pump"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.678479, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 27.028, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Automotive Steering System Component' limit 1", "type": "query", "params": [], "bindings": ["Automotive Steering System Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.694685, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 28.032, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'Automotive Exhaust System Component' limit 1", "type": "query", "params": [], "bindings": ["Automotive Exhaust System Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.705474, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 29.096, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Mechanical Component' limit 1", "type": "query", "params": [], "bindings": ["Mechanical Component"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.718003, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 30.201, "width_percent": 0.643}, {"sql": "select * from `ec_product_categories` where `name` = 'gears' limit 1", "type": "query", "params": [], "bindings": ["gears"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.8407679, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 30.843, "width_percent": 0.803}, {"sql": "select * from `ec_product_categories` where `name` = 'gearbox' limit 1", "type": "query", "params": [], "bindings": ["gearbox"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840094.8426778, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 31.647, "width_percent": 0.783}, {"sql": "select * from `ec_product_categories` where `name` = 'Coupling' limit 1", "type": "query", "params": [], "bindings": ["Coupling"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.154325, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 32.43, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'Force Transducer' limit 1", "type": "query", "params": [], "bindings": ["Force Transducer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.30753, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 33.474, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'High Sensitivity Accelerometer' limit 1", "type": "query", "params": [], "bindings": ["High Sensitivity Accelerometer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.3174012, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 34.538, "width_percent": 0.924}, {"sql": "select * from `ec_product_categories` where `name` = 'High-Temperature Accelerometer' limit 1", "type": "query", "params": [], "bindings": ["High-Temperature Accelerometer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.335767, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 35.462, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Accelerometer' limit 1", "type": "query", "params": [], "bindings": ["Industrial Accelerometer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.354836, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 36.446, "width_percent": 0.863}, {"sql": "select * from `ec_product_categories` where `name` = 'Universal Accelerometer' limit 1", "type": "query", "params": [], "bindings": ["Universal Accelerometer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.3752668, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 37.309, "width_percent": 1.225}, {"sql": "select * from `ec_product_categories` where `name` = 'Triaxial Accelerometer' limit 1", "type": "query", "params": [], "bindings": ["Triaxial Accelerometer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.452729, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 38.534, "width_percent": 0.823}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibration Sensors' limit 1", "type": "query", "params": [], "bindings": ["Vibration Sensors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.4622931, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 39.357, "width_percent": 0.924}, {"sql": "select * from `ec_product_categories` where `name` = 'Signal Conditioners' limit 1", "type": "query", "params": [], "bindings": ["Signal Conditioners"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.471653, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 40.281, "width_percent": 0.703}, {"sql": "select * from `ec_product_categories` where `name` = 'Mounting Accessories' limit 1", "type": "query", "params": [], "bindings": ["Mounting Accessories"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.6197958, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 40.984, "width_percent": 1.185}, {"sql": "select * from `ec_product_categories` where `name` = 'Sensor Cables' limit 1", "type": "query", "params": [], "bindings": ["Sensor Cables"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.6301358, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 42.169, "width_percent": 1.225}, {"sql": "select * from `ec_product_categories` where `name` = 'Connection Accessories' limit 1", "type": "query", "params": [], "bindings": ["Connection Accessories"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.639798, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 43.394, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'Connectors' limit 1", "type": "query", "params": [], "bindings": ["Connectors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840095.659907, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 44.438, "width_percent": 0.964}, {"sql": "select * from `ec_product_categories` where `name` = 'Display Modules' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.020993, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 45.402, "width_percent": 1.245}, {"sql": "select * from `ec_product_categories` where `name` = 'Machine Monitoring and Balancing' limit 1", "type": "query", "params": [], "bindings": ["Machine Monitoring and Balancing"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.05423, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 46.647, "width_percent": 1.124}, {"sql": "select * from `ec_product_categories` where `name` = '4 to 20 mA Transducers' limit 1", "type": "query", "params": [], "bindings": ["4 to 20 mA Transducers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.176228, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 47.771, "width_percent": 0.964}, {"sql": "select * from `ec_product_categories` where `name` = 'Miniature Accelerometers' limit 1", "type": "query", "params": [], "bindings": ["Miniature Accelerometers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.208346, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 48.735, "width_percent": 1.124}, {"sql": "select * from `ec_product_categories` where `name` = 'Shaft Encoders' limit 1", "type": "query", "params": [], "bindings": ["Shaft Encoders"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.2176611, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 49.859, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibration Monitoring Devices' limit 1", "type": "query", "params": [], "bindings": ["Vibration Monitoring Devices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.233706, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 50.803, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibration and Sound Level Meters' limit 1", "type": "query", "params": [], "bindings": ["Vibration and Sound Level Meters"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.243279, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 51.847, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Piezoelectric Accelerometers' limit 1", "type": "query", "params": [], "bindings": ["Piezoelectric Accelerometers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.299324, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 52.952, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Measuring Instruments Accessories' limit 1", "type": "query", "params": [], "bindings": ["Measuring Instruments Accessories"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.3085022, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 53.896, "width_percent": 1.606}, {"sql": "select * from `ec_product_categories` where `name` = 'Measurement Accessories' limit 1", "type": "query", "params": [], "bindings": ["Measurement Accessories"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.411234, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 55.502, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Network Adapter' limit 1", "type": "query", "params": [], "bindings": ["Network Adapter"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.420789, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 56.606, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Software License' limit 1", "type": "query", "params": [], "bindings": ["Software License"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.446011, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 57.711, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Accelerometers' limit 1", "type": "query", "params": [], "bindings": ["Accelerometers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.508169, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 58.715, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibration Calibration Systems' limit 1", "type": "query", "params": [], "bindings": ["Vibration Calibration Systems"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.813461, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 59.719, "width_percent": 2.169}, {"sql": "select * from `ec_product_categories` where `name` = 'Calibration Accessories' limit 1", "type": "query", "params": [], "bindings": ["Calibration Accessories"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.851986, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 61.888, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Cellular Network Modem' limit 1", "type": "query", "params": [], "bindings": ["Cellular Network Modem"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.865649, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 62.871, "width_percent": 1.807}, {"sql": "select * from `ec_product_categories` where `name` = 'Radio Beacon Transmitters' limit 1", "type": "query", "params": [], "bindings": ["Radio Beacon Transmitters"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.87559, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 64.679, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Radio-Controlled Beacon Lights' limit 1", "type": "query", "params": [], "bindings": ["Radio-Controlled Beacon Lights"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.885308, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 65.663, "width_percent": 1.406}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Report Printers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Report Printers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.895224, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 67.068, "width_percent": 0.964}, {"sql": "select * from `ec_product_categories` where `name` = 'Cable-Connected Beacon Lights' limit 1", "type": "query", "params": [], "bindings": ["Cable-Connected Beacon Lights"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.904334, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 68.032, "width_percent": 0.964}, {"sql": "select * from `ec_product_categories` where `name` = 'Software Licenses' limit 1", "type": "query", "params": [], "bindings": ["Software Licenses"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.920545, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 68.996, "width_percent": 0.964}, {"sql": "select * from `ec_product_categories` where `name` = 'Asphalt Repair Technology' limit 1", "type": "query", "params": [], "bindings": ["Asphalt Repair Technology"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840096.936707, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 69.96, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Self-Leveling Wheel Saws' limit 1", "type": "query", "params": [], "bindings": ["Self-Leveling Wheel Saws"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.156998, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 70.944, "width_percent": 1.145}, {"sql": "select * from `ec_product_categories` where `name` = 'Screening Buckets' limit 1", "type": "query", "params": [], "bindings": ["Screening Buckets"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.1660292, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 72.088, "width_percent": 0.904}, {"sql": "select * from `ec_product_categories` where `name` = 'Cutter Heads' limit 1", "type": "query", "params": [], "bindings": ["Cutter Heads"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.175176, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 72.992, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Road Planers' limit 1", "type": "query", "params": [], "bindings": ["Road Planers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.184056, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 74.096, "width_percent": 0.924}, {"sql": "select * from `ec_product_categories` where `name` = 'Wheel Saws' limit 1", "type": "query", "params": [], "bindings": ["Wheel Saws"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.275317, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 75.02, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'Universal Drum Cutters' limit 1", "type": "query", "params": [], "bindings": ["Universal Drum Cutters"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.297836, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 76.084, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Stabilizers' limit 1", "type": "query", "params": [], "bindings": ["Stabilizers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.306009, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 77.028, "width_percent": 0.783}, {"sql": "select * from `ec_product_categories` where `name` = 'Planers' limit 1", "type": "query", "params": [], "bindings": ["Planers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.32603, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 77.811, "width_percent": 0.944}, {"sql": "select * from `ec_product_categories` where `name` = 'Asphalt Float' limit 1", "type": "query", "params": [], "bindings": ["Asphalt Float"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.342325, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 78.755, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Asphalt Granulator Bucket' limit 1", "type": "query", "params": [], "bindings": ["Asphalt Granulator Bucket"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.351609, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 79.759, "width_percent": 0.843}, {"sql": "select * from `ec_product_categories` where `name` = 'Crusher Buckets' limit 1", "type": "query", "params": [], "bindings": ["Crusher Buckets"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.360335, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 80.602, "width_percent": 1.064}, {"sql": "select * from `ec_product_categories` where `name` = 'Universal Screening Buckets' limit 1", "type": "query", "params": [], "bindings": ["Universal Screening Buckets"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.368891, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 81.667, "width_percent": 0.723}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibrating Wheel Compactors' limit 1", "type": "query", "params": [], "bindings": ["Vibrating Wheel Compactors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.39113, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 82.39, "width_percent": 1.104}, {"sql": "select * from `ec_product_categories` where `name` = 'Vibrating Plate Compactors' limit 1", "type": "query", "params": [], "bindings": ["Vibrating Plate Compactors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.407248, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 83.494, "width_percent": 0.904}, {"sql": "select * from `ec_product_categories` where `name` = 'Chain Trenchers' limit 1", "type": "query", "params": [], "bindings": ["Chain Trenchers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.416439, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 84.398, "width_percent": 0.884}, {"sql": "select * from `ec_product_categories` where `name` = 'Wall Grinders' limit 1", "type": "query", "params": [], "bindings": ["Wall Grinders"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.740219, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 85.281, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Beach Cleaner' limit 1", "type": "query", "params": [], "bindings": ["Beach Cleaner"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.755635, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 86.265, "width_percent": 1.024}, {"sql": "select * from `ec_product_categories` where `name` = 'Planers for Special Applications' limit 1", "type": "query", "params": [], "bindings": ["Planers for Special Applications"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.76512, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 87.289, "width_percent": 1.124}, {"sql": "select * from `ec_product_categories` where `name` = 'Pneumatic Valves' limit 1", "type": "query", "params": [], "bindings": ["Pneumatic Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.773515, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 88.414, "width_percent": 0.863}, {"sql": "select * from `ec_product_categories` where `name` = 'Fieldbus Communication Systems' limit 1", "type": "query", "params": [], "bindings": ["Fieldbus Communication Systems"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.782657, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 89.277, "width_percent": 1.044}, {"sql": "select * from `ec_product_categories` where `name` = 'Pneumatic Valves for Hazardous Locations' limit 1", "type": "query", "params": [], "bindings": ["Pneumatic Valves for Hazardous Locations"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.790873, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 90.321, "width_percent": 0.703}, {"sql": "select * from `ec_product_categories` where `name` = 'Pneumatic Solenoid Valves' limit 1", "type": "query", "params": [], "bindings": ["Pneumatic Solenoid Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.7995439, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 91.024, "width_percent": 0.863}, {"sql": "select * from `ec_product_categories` where `name` = 'Small 3-Way Valves' limit 1", "type": "query", "params": [], "bindings": ["Small 3-Way Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.8077002, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 91.888, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'Liquid Valve Series' limit 1", "type": "query", "params": [], "bindings": ["Liquid Valve Series"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.832744, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 92.871, "width_percent": 0.803}, {"sql": "select * from `ec_product_categories` where `name` = 'Pulse Valve' limit 1", "type": "query", "params": [], "bindings": ["Pulse Valve"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.841895, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 93.675, "width_percent": 1.004}, {"sql": "select * from `ec_product_categories` where `name` = 'Proportional Pressure Control Valves' limit 1", "type": "query", "params": [], "bindings": ["Proportional Pressure Control Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.920043, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 94.679, "width_percent": 0.984}, {"sql": "select * from `ec_product_categories` where `name` = 'ISO Standard Valves' limit 1", "type": "query", "params": [], "bindings": ["ISO Standard Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.935821, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 95.663, "width_percent": 0.884}, {"sql": "select * from `ec_product_categories` where `name` = 'Air Control Valves' limit 1", "type": "query", "params": [], "bindings": ["Air Control Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.944643, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 96.546, "width_percent": 0.884}, {"sql": "select * from `ec_product_categories` where `name` = 'Small 4-Way Pneumatic Valves' limit 1", "type": "query", "params": [], "bindings": ["Small 4-Way Pneumatic Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840097.952828, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 97.43, "width_percent": 0.663}, {"sql": "select * from `ec_product_categories` where `name` = 'Compact High-Performance Valves' limit 1", "type": "query", "params": [], "bindings": ["Compact High-Performance Valves"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840098.026557, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 98.092, "width_percent": 1.084}, {"sql": "select * from `ec_product_categories` where `name` = 'Solenoid Valves' limit 1", "type": "query", "params": [], "bindings": ["Solenoid Val<PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840098.0354788, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 99.177, "width_percent": 0.823}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 91, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttributeSet": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttributeSet.php&line=1", "ajax": false, "filename": "ProductAttributeSet.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttribute": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttribute.php&line=1", "ajax": false, "filename": "ProductAttribute.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 119, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData", "uri": "POST admin/tools/data-synchronize/import/products/validate", "permission": "ecommerce.import.products.index", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/tools/data-synchronize/import/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:32-68</a>", "middleware": "web, core, auth", "duration": "23.1s", "peak_memory": "72MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1318686179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1318686179\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1352920239 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"72 characters\">products_export_2025-06-13_22-22-03-17b3307dce5b9af1abdbff95899fcd70.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2500</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352920239\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1378780517 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IktiUDFneS9DUGV6Mk51cU9zS3VCL1E9PSIsInZhbHVlIjoieTJSUWV0ZDFOd0dWa0p4bE5FSkROT2REZXEwNkg5MVlzdlVRNHBjQ0ZTb0Z1U0pWOXBGZ3NiN0swbkcvL3o0bVYrRkg3d1FMTk8rdGthMU5pVGE3VnB4c2xhRGtTK1VTcXNITjVQSW1lc2RyUjlmZ2hIVWo4Q0lWcm9ycURpbGIiLCJtYWMiOiIyYjE0MzBiZGVkMWUxYWJmYTE2YzRhNjZjZWZkN2YxMjMwNTA4NzUyNzJlN2EzZDA3NWIzMDdhN2YwMGFlZjM2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">731</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarycxYiE3zmGjqUA4w4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktiUDFneS9DUGV6Mk51cU9zS3VCL1E9PSIsInZhbHVlIjoieTJSUWV0ZDFOd0dWa0p4bE5FSkROT2REZXEwNkg5MVlzdlVRNHBjQ0ZTb0Z1U0pWOXBGZ3NiN0swbkcvL3o0bVYrRkg3d1FMTk8rdGthMU5pVGE3VnB4c2xhRGtTK1VTcXNITjVQSW1lc2RyUjlmZ2hIVWo4Q0lWcm9ycURpbGIiLCJtYWMiOiIyYjE0MzBiZGVkMWUxYWJmYTE2YzRhNjZjZWZkN2YxMjMwNTA4NzUyNzJlN2EzZDA3NWIzMDdhN2YwMGFlZjM2IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IndUa3JPaExDbm1lcEVyRGQvalA0d2c9PSIsInZhbHVlIjoiRzFtWU0rczFoY1RoUDV6MytsZXBzS0JaS01aSHZqL2hqVjZHSUhpOXVYbHh0ZjJ2eENhR0pUSXBueThyb1h1eHhNcDdKOU8rZU05UGFPNUEyRm9YakVyN2JjOWdWcFR5dzVUWnp4OEY3OHU2dFY0ZDZBZVNFdUk4cGxFU2FTbFkiLCJtYWMiOiI0ZjNhMDE3Njg2OWUzMTAzNTY4MDg2YzJiZDBkOWM4ODA4YjI4MGIyYTdmMTE2MzU5ZjM5ZDc3NzkxY2QwZmIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378780517\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-808779549 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808779549\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-98165116 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 18:41:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98165116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-923883159 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923883159\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData"}, "badge": null}}