{"__meta": {"id": "01JXN9XTA69B8FK9FYXW0SF1XN", "datetime": "2025-06-13 18:48:08", "utime": **********.775064, "method": "POST", "uri": "/admin/tools/data-synchronize/import/products/validate", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.200084, "end": **********.775077, "duration": 25.574993133544922, "duration_str": "25.57s", "measures": [{"label": "Booting", "start": **********.200084, "relative_start": 0, "end": **********.954129, "relative_end": **********.954129, "duration": 0.****************, "duration_str": "754ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.95414, "relative_start": 0.****************, "end": **********.775079, "relative_end": 1.9073486328125e-06, "duration": 24.**************, "duration_str": "24.82s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.971886, "relative_start": 0.****************, "end": **********.979275, "relative_end": **********.979275, "duration": 0.007389068603515625, "duration_str": "7.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.771186, "relative_start": 25.***************, "end": **********.772756, "relative_end": **********.772756, "duration": 0.001569986343383789, "duration_str": "1.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "65MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 133, "nb_statements": 133, "nb_visible_statements": 133, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.21967000000000006, "accumulated_duration_str": "220ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 33 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.9895499, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.196}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.9950042, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.196, "width_percent": 0.127}, {"sql": "select * from `ec_product_attribute_sets`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": 1749840464.0210142, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.323, "width_percent": 0.205}, {"sql": "select * from `ec_product_attributes` where `ec_product_attributes`.`attribute_set_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": 1749840464.0257251, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.528, "width_percent": 0.214}, {"sql": "select * from `ec_taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": 1749840464.029873, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.742, "width_percent": 0.241}, {"sql": "select `lang_code` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 94}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": 1749840464.032546, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.983, "width_percent": 0.341}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → Skin Surgery Machine' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → Skin Surgery Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.199298, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.325, "width_percent": 0.241}, {"sql": "select * from `ec_product_categories` where `name` = 'Ophthalmology & Optometry' limit 1", "type": "query", "params": [], "bindings": ["Ophthalmology & Optometry"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.228352, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.566, "width_percent": 0.223}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → ENT Navigation System' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → ENT Navigation System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.237058, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.789, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → ENT Workstation' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → ENT Workstation"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.327894, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.581, "width_percent": 0.819}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → Skin & Obesity Treatment Device' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → Skin & Obesity Treatment Device"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.351455, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.401, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → ENT Visual System' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → ENT Visual System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.3956192, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.193, "width_percent": 0.924}, {"sql": "select * from `ec_product_categories` where `name` = 'Lens Edging Solution' limit 1", "type": "query", "params": [], "bindings": ["Lens Edging Solution"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.44576, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.117, "width_percent": 1.011}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental Device' limit 1", "type": "query", "params": [], "bindings": ["Dental Device"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.5832212, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.127, "width_percent": 1.002}, {"sql": "select * from `ec_product_categories` where `name` = 'Medical Equipment → ENT System' limit 1", "type": "query", "params": [], "bindings": ["Medical Equipment → ENT System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.5940182, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.129, "width_percent": 0.765}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental CAD/CAM Milling Machine' limit 1", "type": "query", "params": [], "bindings": ["Dental CAD/CAM Milling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.675581, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.894, "width_percent": 0.778}, {"sql": "select * from `ec_product_categories` where `name` = 'Pumps' limit 1", "type": "query", "params": [], "bindings": ["Pumps"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.6945999, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.672, "width_percent": 0.191}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental CAD/CAM 5-axis milling machine' limit 1", "type": "query", "params": [], "bindings": ["Dental CAD/CAM 5-axis milling machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.718151, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.863, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental CBCT Imaging System' limit 1", "type": "query", "params": [], "bindings": ["Dental CBCT Imaging System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.7280478, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.66, "width_percent": 0.751}, {"sql": "select * from `ec_product_categories` where `name` = '2D Digital Panoramic X-ray System' limit 1", "type": "query", "params": [], "bindings": ["2D Digital Panoramic X-ray System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.7539961, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.411, "width_percent": 0.76}, {"sql": "select * from `ec_product_categories` where `name` = 'Intraoral Digital X-ray Sensor' limit 1", "type": "query", "params": [], "bindings": ["Intraoral Digital X-ray Sensor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.763595, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.171, "width_percent": 0.851}, {"sql": "select * from `ec_product_categories` where `name` = 'Handheld Intraoral X-ray Device' limit 1", "type": "query", "params": [], "bindings": ["Handheld Intraoral X-ray Device"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.774475, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.023, "width_percent": 0.788}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental Imaging & Consultation Software' limit 1", "type": "query", "params": [], "bindings": ["Dental Imaging & Consultation Software"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.788053, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.81, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental Imaging & Diagnostic Software' limit 1", "type": "query", "params": [], "bindings": ["Dental Imaging & Diagnostic Software"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.79805, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.607, "width_percent": 0.801}, {"sql": "select * from `ec_product_categories` where `name` = 'Orthodontic Analysis Software' limit 1", "type": "query", "params": [], "bindings": ["Orthodontic Analysis Software"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.808347, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.408, "width_percent": 0.91}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental CAD/CAM 3D Scanner' limit 1", "type": "query", "params": [], "bindings": ["Dental CAD/CAM 3D Scanner"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.818945, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.318, "width_percent": 0.838}, {"sql": "select * from `ec_product_categories` where `name` = 'CAD/CAM Dental Milling Machine' limit 1", "type": "query", "params": [], "bindings": ["CAD/CAM Dental Milling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.8360898, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.156, "width_percent": 0.742}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental Sintering Furnace' limit 1", "type": "query", "params": [], "bindings": ["Dental Sintering Furnace"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.872389, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.898, "width_percent": 0.81}, {"sql": "select * from `ec_product_categories` where `name` = 'Dental CAM Software' limit 1", "type": "query", "params": [], "bindings": ["Dental CAM Software"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.882194, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.708, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Semi-Automatic Round Bottle Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Semi-Automatic Round Bottle Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.9783611, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.5, "width_percent": 0.783}, {"sql": "select * from `ec_product_categories` where `name` = 'Semi-Automatic Wrap-Around Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Semi-Automatic Wrap-Around Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840469.994832, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.283, "width_percent": 0.819}, {"sql": "select * from `ec_product_categories` where `name` = 'Top Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Top Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.032973, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.103, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Syringe Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Syringe Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.062885, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.9, "width_percent": 0.819}, {"sql": "select * from `ec_product_categories` where `name` = 'Specialty Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Specialty Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.072692, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.719, "width_percent": 0.737}, {"sql": "select * from `ec_product_categories` where `name` = 'Pivot Beak Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Pivot Beak Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.08245, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 22.456, "width_percent": 0.742}, {"sql": "select * from `ec_product_categories` where `name` = 'RFID Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["RFID Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.091624, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 23.198, "width_percent": 0.778}, {"sql": "select * from `ec_product_categories` where `name` = 'Speed Sprayer' limit 1", "type": "query", "params": [], "bindings": ["Speed Sprayer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.101504, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 23.977, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Circular Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Circular Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.144864, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 24.769, "width_percent": 0.774}, {"sql": "select * from `ec_product_categories` where `name` = 'Sprayer' limit 1", "type": "query", "params": [], "bindings": ["Sprayer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.15516, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.543, "width_percent": 0.255}, {"sql": "select * from `ec_product_categories` where `name` = 'Palm Plantation' limit 1", "type": "query", "params": [], "bindings": ["Palm Plantation"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.211223, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.798, "width_percent": 0.879}, {"sql": "select * from `ec_product_categories` where `name` = 'Side Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Side Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.2602432, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 26.676, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = 'Mini-Dumper' limit 1", "type": "query", "params": [], "bindings": ["Mini-Dumper"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.277268, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 27.519, "width_percent": 0.829}, {"sql": "select * from `ec_product_categories` where `name` = 'Automatic Feeding System' limit 1", "type": "query", "params": [], "bindings": ["Automatic Feeding System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.302923, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 28.347, "width_percent": 0.874}, {"sql": "select * from `ec_product_categories` where `name` = 'Label Applicator for Form-Fill-Seal Machines' limit 1", "type": "query", "params": [], "bindings": ["Label Applicator for Form-Fill-Seal Machines"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.3125908, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 29.221, "width_percent": 0.737}, {"sql": "select * from `ec_product_categories` where `name` = 'Forklift' limit 1", "type": "query", "params": [], "bindings": ["Forklift"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.322536, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 29.959, "width_percent": 0.719}, {"sql": "select * from `ec_product_categories` where `name` = 'Skid Loader' limit 1", "type": "query", "params": [], "bindings": ["Skid <PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.338925, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 30.678, "width_percent": 0.901}, {"sql": "select * from `ec_product_categories` where `name` = 'Semi-Automatic Labeling Machine' limit 1", "type": "query", "params": [], "bindings": ["Semi-Automatic Labeling Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.350355, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 31.579, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Food-packaging equipment → Modified-Atmosphere Packaging (MAP) / tray-sealing machines' limit 1", "type": "query", "params": [], "bindings": ["Food-packaging equipment → Modified-Atmosphere Packaging (MAP) / tray-sealing machines"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.359896, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 32.371, "width_percent": 0.737}, {"sql": "select * from `ec_product_categories` where `name` = 'Food-packaging equipment → Vacuum Skin Packaging (VSP) Machines' limit 1", "type": "query", "params": [], "bindings": ["Food-packaging equipment → Vacuum Skin Packaging (VSP) Machines"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.370058, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 33.109, "width_percent": 0.783}, {"sql": "select * from `ec_product_categories` where `name` = 'Modified Atmosphere Packaging (MAP) Equipment' limit 1", "type": "query", "params": [], "bindings": ["Modified Atmosphere Packaging (MAP) Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.380062, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 33.892, "width_percent": 0.874}, {"sql": "select * from `ec_product_categories` where `name` = 'Vacuum Skin Packaging (VSP) Equipment' limit 1", "type": "query", "params": [], "bindings": ["Vacuum Skin Packaging (VSP) Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.411634, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 34.766, "width_percent": 0.801}, {"sql": "select * from `ec_product_categories` where `name` = 'Tray Sealing Equipment' limit 1", "type": "query", "params": [], "bindings": ["Tray Sealing Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.443278, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 35.567, "width_percent": 0.856}, {"sql": "select * from `ec_product_categories` where `name` = 'Thermoforming Packaging Equipment' limit 1", "type": "query", "params": [], "bindings": ["Thermoforming Packaging Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.468312, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 36.423, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Vacuum Packaging Equipment' limit 1", "type": "query", "params": [], "bindings": ["Vacuum Packaging Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.485508, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 37.219, "width_percent": 0.747}, {"sql": "select * from `ec_product_categories` where `name` = 'Textile industry' limit 1", "type": "query", "params": [], "bindings": ["Textile industry"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.522786, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 37.966, "width_percent": 0.824}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Vacuum Packaging Equipment' limit 1", "type": "query", "params": [], "bindings": ["Industrial Vacuum Packaging Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.641251, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 38.79, "width_percent": 0.246}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Vision Inspection & Gate-Cutting Equipment' limit 1", "type": "query", "params": [], "bindings": ["Industrial Vision Inspection & Gate-Cutting Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840470.656997, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 39.036, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Nonwoven/Filter Field' limit 1", "type": "query", "params": [], "bindings": ["Nonwoven/Filter Field"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.123979, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 39.832, "width_percent": 0.783}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Process Cooling / Chillers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Process Cooling / Chillers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.148564, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 40.615, "width_percent": 0.788}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Process Cooling / Inverter Chillers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Process Cooling / Inverter Chillers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.1732159, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 41.403, "width_percent": 0.806}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Process Cooling / Laser Chillers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Process Cooling / Laser Chillers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.190274, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 42.209, "width_percent": 0.806}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Process Cooling / Heating & Cooling Combination Chillers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Process Cooling / Heating & Cooling Combination Chillers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.216076, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 43.015, "width_percent": 1.37}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Air Treatment / Air-Type Chillers' limit 1", "type": "query", "params": [], "bindings": ["Industrial Air Treatment / Air-Type Chillers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.2348852, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 44.385, "width_percent": 0.765}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Oil Chiller' limit 1", "type": "query", "params": [], "bindings": ["Industrial Oil Chiller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.2447, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 45.15, "width_percent": 0.747}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Immersion Chiller' limit 1", "type": "query", "params": [], "bindings": ["Industrial Immersion Chiller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.255178, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 45.896, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Solvent Recovery System' limit 1", "type": "query", "params": [], "bindings": ["Industrial Solvent Recovery System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.272559, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 46.693, "width_percent": 0.788}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Battery Cooling System' limit 1", "type": "query", "params": [], "bindings": ["Industrial Battery Cooling System"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.28248, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 47.48, "width_percent": 0.901}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Oil Temperature Controller' limit 1", "type": "query", "params": [], "bindings": ["Industrial Oil Temperature Controller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.292556, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 48.382, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Water Temperature Controller' limit 1", "type": "query", "params": [], "bindings": ["Industrial Water Temperature Controller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.310173, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 49.224, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Hydrogen Cooling Systems' limit 1", "type": "query", "params": [], "bindings": ["Industrial Hydrogen Cooling Systems"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.3253698, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 50.066, "width_percent": 0.824}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Checkweighing Equipment' limit 1", "type": "query", "params": [], "bindings": ["Industrial Checkweighing Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.3359149, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 50.89, "width_percent": 0.833}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Metal Detection Equipment' limit 1", "type": "query", "params": [], "bindings": ["Industrial Metal Detection Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.346086, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 51.723, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Inspection Equipment' limit 1", "type": "query", "params": [], "bindings": ["Industrial Inspection Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.356837, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 52.565, "width_percent": 0.847}, {"sql": "select * from `ec_product_categories` where `name` = 'Industrial Packaging Machinery' limit 1", "type": "query", "params": [], "bindings": ["Industrial Packaging Machinery"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.3745022, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 53.412, "width_percent": 0.856}, {"sql": "select * from `ec_product_categories` where `name` = 'Pharmaceutical Equipment' limit 1", "type": "query", "params": [], "bindings": ["Pharmaceutical Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.457938, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 54.268, "width_percent": 0.815}, {"sql": "select * from `ec_product_categories` where `name` = 'Sewing Winder' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.514175, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 55.083, "width_percent": 1.124}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Induction Motor' limit 1", "type": "query", "params": [], "bindings": ["AC Induction Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.613549, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 56.207, "width_percent": 0.865}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Input Type BLDC Motor' limit 1", "type": "query", "params": [], "bindings": ["AC Input Type BLDC Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.631133, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 57.072, "width_percent": 1.161}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Motor Speed Controller' limit 1", "type": "query", "params": [], "bindings": ["AC Motor Speed Controller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.67102, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 58.233, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = 'Shaded Pole Induction Motor' limit 1", "type": "query", "params": [], "bindings": ["Shaded Pole Induction Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.7166321, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 59.025, "width_percent": 0.851}, {"sql": "select * from `ec_product_categories` where `name` = 'Shaded Pole Induction Motor with Brake' limit 1", "type": "query", "params": [], "bindings": ["Shaded Pole Induction Motor with <PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.726393, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 59.876, "width_percent": 0.765}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Induction Geared Motor' limit 1", "type": "query", "params": [], "bindings": ["AC Induction Geared Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.7552128, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 60.641, "width_percent": 0.833}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Induction Geared Motor with Terminal Box' limit 1", "type": "query", "params": [], "bindings": ["AC Induction Geared Motor with Terminal Box"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.765858, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 61.474, "width_percent": 0.988}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Reversible Geared Motor with Terminal Box' limit 1", "type": "query", "params": [], "bindings": ["AC Reversible Geared Motor with Terminal Box"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.785747, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 62.462, "width_percent": 1.593}, {"sql": "select * from `ec_product_categories` where `name` = 'AC Speed Control Geared Motor with Brake' limit 1", "type": "query", "params": [], "bindings": ["AC Speed Control Geared Motor with Brake"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.800318, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 64.055, "width_percent": 1.593}, {"sql": "select * from `ec_product_categories` where `name` = 'DC Standard Brush Motor' limit 1", "type": "query", "params": [], "bindings": ["DC Standard Brush Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.82126, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 65.648, "width_percent": 1.457}, {"sql": "select * from `ec_product_categories` where `name` = 'Special DC Motor' limit 1", "type": "query", "params": [], "bindings": ["Special DC Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.842086, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 67.105, "width_percent": 0.769}, {"sql": "select * from `ec_product_categories` where `name` = 'Parallel Shaft Gearhead' limit 1", "type": "query", "params": [], "bindings": ["Parallel Shaft Gearhead"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.8526719, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 67.875, "width_percent": 0.751}, {"sql": "select * from `ec_product_categories` where `name` = 'Right-Angle Hollow Shaft Gearhead' limit 1", "type": "query", "params": [], "bindings": ["Right-Angle Hollow Shaft Gearhead"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.8843598, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 68.626, "width_percent": 0.751}, {"sql": "select * from `ec_product_categories` where `name` = 'Flat Type Gearhead' limit 1", "type": "query", "params": [], "bindings": ["Flat Type Gearhead"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.90194, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 69.377, "width_percent": 0.847}, {"sql": "select * from `ec_product_categories` where `name` = 'Neelde Punching' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>ing"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.911926, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 70.224, "width_percent": 0.765}, {"sql": "select * from `ec_product_categories` where `name` = 'Worm Gear Reducer' limit 1", "type": "query", "params": [], "bindings": ["Worm Gear Reducer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.924017, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 70.988, "width_percent": 0.706}, {"sql": "select * from `ec_product_categories` where `name` = 'Planetary Geared DC Motor' limit 1", "type": "query", "params": [], "bindings": ["Planetary Geared DC Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.935592, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 71.694, "width_percent": 0.806}, {"sql": "select * from `ec_product_categories` where `name` = 'Micro Brushless DC (BLDC) Motor' limit 1", "type": "query", "params": [], "bindings": ["Micro Brushless DC (BLDC) Motor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840471.988782, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 72.5, "width_percent": 0.838}, {"sql": "select * from `ec_product_categories` where `name` = 'Semi-Automatic Screen Printing Machine' limit 1", "type": "query", "params": [], "bindings": ["Semi-Automatic Screen Printing Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.0231652, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 73.337, "width_percent": 1.479}, {"sql": "select * from `ec_product_categories` where `name` = 'Roller' limit 1", "type": "query", "params": [], "bindings": ["Roller"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.057766, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 74.817, "width_percent": 0.191}, {"sql": "select * from `ec_product_categories` where `name` = 'Feeder' limit 1", "type": "query", "params": [], "bindings": ["<PERSON>ede<PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.074614, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 75.008, "width_percent": 0.209}, {"sql": "select * from `ec_product_categories` where `name` = 'cutter' limit 1", "type": "query", "params": [], "bindings": ["cutter"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.0845258, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 75.217, "width_percent": 0.269}, {"sql": "select * from `ec_product_categories` where `name` = 'Automatic Screen Printing Machine' limit 1", "type": "query", "params": [], "bindings": ["Automatic Screen Printing Machine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.1084712, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 75.486, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = 'Filters' limit 1", "type": "query", "params": [], "bindings": ["Filters"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749840472.14021, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 76.328, "width_percent": 0.264}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.157752, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.592, "width_percent": 0.788}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.174355, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.38, "width_percent": 0.869}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.244865, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.249, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.2751842, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.046, "width_percent": 0.769}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.319132, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.815, "width_percent": 0.765}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.348429, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.58, "width_percent": 0.892}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.462939, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.472, "width_percent": 0.901}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.5094528, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.374, "width_percent": 0.756}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.525694, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.129, "width_percent": 0.742}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.5423071, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.871, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.551621, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.713, "width_percent": 0.824}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.560802, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.537, "width_percent": 0.81}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.57747, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.348, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.587317, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.14, "width_percent": 0.214}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.594843, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.354, "width_percent": 0.71}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.624617, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.064, "width_percent": 0.223}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.6736891, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.287, "width_percent": 0.842}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.718394, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.129, "width_percent": 0.205}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.740498, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.334, "width_percent": 0.241}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.778976, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.575, "width_percent": 0.815}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.811019, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.39, "width_percent": 0.797}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.841373, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.187, "width_percent": 0.833}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.87141, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.02, "width_percent": 0.751}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.880123, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.771, "width_percent": 0.742}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.889258, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.513, "width_percent": 0.792}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840472.91285, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.305, "width_percent": 0.751}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.072907, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.056, "width_percent": 0.815}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.089216, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.871, "width_percent": 0.815}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.119709, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.686, "width_percent": 0.829}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.1488461, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.514, "width_percent": 0.774}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.1580498, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.288, "width_percent": 0.747}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.1670098, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.035, "width_percent": 0.178}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749840473.189085, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.212, "width_percent": 0.788}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttributeSet": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttributeSet.php&line=1", "ajax": false, "filename": "ProductAttributeSet.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttribute": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttribute.php&line=1", "ajax": false, "filename": "ProductAttribute.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 41, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData", "uri": "POST admin/tools/data-synchronize/import/products/validate", "permission": "ecommerce.import.products.index", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/tools/data-synchronize/import/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:32-68</a>", "middleware": "web, core, auth", "duration": "25.58s", "peak_memory": "72MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1308501670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1308501670\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-106474484 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"72 characters\">products_export_2025-06-13_22-22-03-17b3307dce5b9af1abdbff95899fcd70.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10000</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106474484\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1652230219 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdwZXhwT1FucGh5eUNCUE9aelZXUEE9PSIsInZhbHVlIjoiODBweU9OQ2pPN3RuT0RMSzhWVDBqeW01dHpwazVhbXQ5MkVqZmRPZGZ4R3g0L3VXdVU3d0doZjkvNFZrYjkreWJRN3pzRWN0TStMZnFJcW1OYUJqNTk3TTlsQUo5bDNKRFdpYVZOZnRBSDkzZ1RwMkV0MlZRR3FvVUYxajNVUDQiLCJtYWMiOiJjYzYzZDliZWI1OWM2ZjMyNmIzNGY4M2ZiNDc0NmZhMjhkOWNkY2E4MTNkZTk0NTU0OGE1YTJiMGFhNWVkMzkzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">732</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryQiwTPu3IPejmXbGv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImdwZXhwT1FucGh5eUNCUE9aelZXUEE9PSIsInZhbHVlIjoiODBweU9OQ2pPN3RuT0RMSzhWVDBqeW01dHpwazVhbXQ5MkVqZmRPZGZ4R3g0L3VXdVU3d0doZjkvNFZrYjkreWJRN3pzRWN0TStMZnFJcW1OYUJqNTk3TTlsQUo5bDNKRFdpYVZOZnRBSDkzZ1RwMkV0MlZRR3FvVUYxajNVUDQiLCJtYWMiOiJjYzYzZDliZWI1OWM2ZjMyNmIzNGY4M2ZiNDc0NmZhMjhkOWNkY2E4MTNkZTk0NTU0OGE1YTJiMGFhNWVkMzkzIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IktkOXRZdTdlZ2I5ZUVRNGxPRWpkNEE9PSIsInZhbHVlIjoiNE1SaDN2OUZVRGhRMjdrcHN6UVAycW9rTTJYR3RxOEhZWG8xc0hUa0NkQk9iVGFhTHIvTnJZQ001ZElMcGJpenlEZVRHNDZoVVVVTWo2NkN6eGxTeGowQkFEQmhKQy8zZytnbGdlcXVDZEdXcDU0QWpkeUIxYVNXcml0a05FVHUiLCJtYWMiOiI3YjcwZDFmODlhYzUzYzk5OWRhOWZlMDJjZmY4YTA4OGVmYjFmMTEwZjU2NjM4YTNkOTQ4NGNmZGYwZTVkNmQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652230219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-379851426 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379851426\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 18:48:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-704836837 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704836837\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData"}, "badge": null}}