{"__meta": {"id": "01JXNBB364T9Y9RPPDVFMJDXTM", "datetime": "2025-06-13 19:12:52", "utime": **********.42137, "method": "POST", "uri": "/admin/tools/data-synchronize/import/products/validate", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.041666, "end": **********.421384, "duration": 17.37971806526184, "duration_str": "17.38s", "measures": [{"label": "Booting", "start": **********.041666, "relative_start": 0, "end": **********.833992, "relative_end": **********.833992, "duration": 0.****************, "duration_str": "792ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.834006, "relative_start": 0.****************, "end": **********.421386, "relative_end": 1.9073486328125e-06, "duration": 16.***************, "duration_str": "16.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.857809, "relative_start": 0.****************, "end": **********.86662, "relative_end": **********.86662, "duration": 0.008810997009277344, "duration_str": "8.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.417197, "relative_start": 17.**************, "end": **********.418803, "relative_end": **********.418803, "duration": 0.001605987548828125, "duration_str": "1.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "66MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 590, "nb_visible_statements": 500, "nb_excluded_statements": 90, "nb_failed_statements": 0, "accumulated_duration": 0.22091000000000008, "accumulated_duration_str": "221ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.880023, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.231}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.8884811, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.231, "width_percent": 0.204}, {"sql": "select * from `ec_product_attribute_sets`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": **********.918442, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.435, "width_percent": 0.217}, {"sql": "select * from `ec_product_attributes` where `ec_product_attributes`.`attribute_set_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 89}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": **********.9240649, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.652, "width_percent": 0.181}, {"sql": "select * from `ec_taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 134}], "start": **********.927964, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.833, "width_percent": 0.172}, {"sql": "select `lang_code` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 94}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 57}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ImportProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ImportProductController.php", "line": 14}], "start": **********.9306328, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.005, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-G2OHA') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-G2OHA"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.6033, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 1.191, "width_percent": 0.312}, {"sql": "select * from `ec_product_categories` where `name` = 'Monitors' limit 1", "type": "query", "params": [], "bindings": ["Monitors"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.605767, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.503, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-5VWKY') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-5VWKY"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.61848, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 1.716, "width_percent": 0.263}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-JSFDR') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-JSFDR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.628696, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 1.978, "width_percent": 0.258}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-YRO2S') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-YRO2S"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.638131, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 2.236, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-QX0MS') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-QX0MS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.6475952, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 2.485, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-F0QJZ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-F0QJZ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.656793, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 2.707, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-MGLXQ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-MGLXQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.666202, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 2.924, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-ANMN0') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-ANMN0"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.677679, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 3.142, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-WFB7I') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-WFB7I"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.687062, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 3.377, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-YAXK1') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-YAXK1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.696716, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 3.599, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-EAG1N') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-EAG1N"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.706531, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 3.83, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-8DJNC') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-8DJNC"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.71583, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 4.056, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-GSAT5') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-GSAT5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.725194, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 4.273, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-AQIEB') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-AQIEB"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.734547, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 4.518, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-I0BRO') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-I0BRO"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.743982, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 4.758, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-KZYWQ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-KZYWQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.753504, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 4.993, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-WWEDP') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-WWEDP"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.7627242, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 5.206, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-93ZZX') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-93ZZX"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.772174, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 5.405, "width_percent": 0.258}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-62HJW') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-62HJW"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.781631, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 5.663, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-N2REH') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-N2REH"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.7914371, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 5.894, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-4I6CC') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-4I6CC"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.8022032, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 6.107, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-QQSDQ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-QQSDQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.811415, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 6.319, "width_percent": 0.217}, {"sql": "select * from `ec_product_categories` where `name` = 'Subsea Lifting and Handling Equipment' limit 1", "type": "query", "params": [], "bindings": ["Subsea Lifting and Handling Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.8129191, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.537, "width_percent": 0.122}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-LM5SJ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-LM5SJ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.822614, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 6.659, "width_percent": 0.253}, {"sql": "select * from `ec_product_categories` where `name` = 'Subsea Hydraulic Lights' limit 1", "type": "query", "params": [], "bindings": ["Subsea Hydraulic Lights"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.824241, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.912, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-TXEAC') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-TXEAC"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.833802, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 7.071, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-GTWEV') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-GTWEV"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.84312, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 7.315, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-DIIEE') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-DIIEE"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.8574002, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 7.555, "width_percent": 0.294}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-TKR5B') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-TKR5B"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.868734, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 7.849, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-UNMPS') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-UNMPS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.878257, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 8.094, "width_percent": 0.199}, {"sql": "select * from `ec_product_categories` where `name` = 'Underwater Cameras / Imaging Systems' limit 1", "type": "query", "params": [], "bindings": ["Underwater Cameras / Imaging Systems"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.879689, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.293, "width_percent": 0.109}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-RDZ8O') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-RDZ8O"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.888905, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 8.402, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-A3BRC') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-A3BRC"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.898238, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 8.61, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-7ZET1') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-7ZET1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.907511, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 8.841, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-EA0KF') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-EA0KF"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.916937, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 9.053, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = 'Hydraulic Cylinder' limit 1", "type": "query", "params": [], "bindings": ["Hydraulic Cylinder"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.918438, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.284, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-RRUXV') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-RRUXV"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.927917, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 9.438, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-D35DN') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-D35DN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.9372969, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 9.692, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-IFIQR') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-IFIQR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.946932, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 9.959, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-JFN8X') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-JFN8X"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.95603, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 10.172, "width_percent": 0.276}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-HWVL3') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-HWVL3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.965366, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 10.448, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-OWWVR') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-OWWVR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.976912, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 10.701, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-SPHN8') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-SPHN8"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.986186, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 10.968, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-JZUGS') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-JZUGS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841956.99541, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 11.204, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-XYW1A') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-XYW1A"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.004769, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 11.448, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-H1D2W') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-H1D2W"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.013883, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 11.652, "width_percent": 0.222}, {"sql": "select * from `ec_product_categories` where `name` = 'Cameras' limit 1", "type": "query", "params": [], "bindings": ["Cameras"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.015381, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.874, "width_percent": 0.127}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-KKWCJ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-KKWCJ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.0248451, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 12, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-KTE8H') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-KTE8H"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.0340571, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 12.218, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-1AEYB') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-1AEYB"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.043456, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 12.417, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-SSLH3') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-SSLH3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.052752, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 12.616, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-N1I5B') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-N1I5B"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.061945, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 12.851, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-BHUWQ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-BHUWQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.071273, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 13.087, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-KUYP6') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-KUYP6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.080268, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 13.318, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-ELWG5') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-ELWG5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.089607, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 13.562, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-OMKWP') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-OMKWP"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.09906, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 13.807, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-G1FQB') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-G1FQB"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.108275, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 14.051, "width_percent": 0.253}, {"sql": "select * from `ec_product_categories` where `name` = 'Module' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.109876, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.304, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-SOFJY') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-SOFJY"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.119504, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 14.476, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-RJFPX') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-RJFPX"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.128706, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 14.725, "width_percent": 0.389}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-E3YRW') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-E3YRW"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.1400309, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 15.115, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-K3UYF') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-K3UYF"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.1553981, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 15.368, "width_percent": 0.299}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-MY346') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-MY346"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.1691449, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 15.667, "width_percent": 0.263}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-MGPBO') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-MGPBO"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.179764, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 15.93, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-HKNZE') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-HKNZE"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.189063, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 16.174, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = 'Hydraulic Valves' limit 1", "type": "query", "params": [], "bindings": ["Hydraulic Val<PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.1906002, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.409, "width_percent": 0.122}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-WGYHS') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-WGYHS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.200119, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 16.532, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-HFDVS') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-HFDVS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.208936, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 16.69, "width_percent": 0.131}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-E0YQN') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-E0YQN"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.217852, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 16.821, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-EDB83') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-EDB83"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.22729, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 17.057, "width_percent": 0.244}, {"sql": "select * from `ec_product_categories` where `name` = 'Hydraulic Power Units' limit 1", "type": "query", "params": [], "bindings": ["Hydraulic Power Units"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.228854, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.301, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-UUNVQ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-UUNVQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.2381568, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 17.464, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-DNOG7') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-DNOG7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.247706, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 17.731, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-AEDSJ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-AEDSJ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.2573102, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 17.958, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-SYVMW') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-SYVMW"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.2664762, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 18.166, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-RSPWR') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-RSPWR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.275958, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 18.379, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-U7KAI') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-U7KAI"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.285409, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 18.632, "width_percent": 0.208}, {"sql": "select * from `ec_product_categories` where `name` = 'Hydraulic Support Services' limit 1", "type": "query", "params": [], "bindings": ["Hydraulic Support Services"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.286917, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.84, "width_percent": 0.136}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-KHL4P') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-KHL4P"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.2963371, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 18.976, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-MEBNP') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-MEBNP"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.305494, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 19.207, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-BCA7B') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-BCA7B"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.314691, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 19.447, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-Z4MGV') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-Z4MGV"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.324222, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 19.691, "width_percent": 0.285}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-REBOM') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-REBOM"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.333815, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 19.976, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-LM6UJ') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-LM6UJ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.343001, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 20.176, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-ULTQF') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-ULTQF"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.3526282, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 20.348, "width_percent": 0.199}, {"sql": "select * from `ec_product_categories` where `name` = 'Sensor Controllers' limit 1", "type": "query", "params": [], "bindings": ["Sensor Controllers"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.354134, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.547, "width_percent": 0.127}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-WLE8S') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-WLE8S"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.363221, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 20.674, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-ARIXG') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-ARIXG"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.373987, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 20.837, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-YKQMD') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-YKQMD"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.383893, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 21.063, "width_percent": 0.285}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-L9F7F') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-L9F7F"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.394936, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 21.348, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = 'Subsea Equipment' limit 1", "type": "query", "params": [], "bindings": ["Subsea Equipment"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.396584, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.583, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = 'MF-2443-MLGGB') as `exists`", "type": "query", "params": [], "bindings": ["MF-2443-MLGGB"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 946}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 602}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.406737, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:729", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 729}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=729", "ajax": false, "filename": "Product.php", "line": "729"}, "connection": "muhrak", "explain": null, "start_percent": 21.755, "width_percent": 0.222}, {"sql": "select * from `ec_product_categories` where `name` = 'Analyzer' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 1085}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Importers/ProductImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Importers\\ProductImporter.php", "line": 603}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 290}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 162}], "start": 1749841957.408335, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.977, "width_percent": 0.149}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.418594, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.127, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.427655, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.367, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.4362519, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.616, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.4447799, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.86, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.453094, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.104, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.4613872, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.326, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.4691842, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.539, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.477689, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.743, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.485799, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.955, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.4941401, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.173, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.502278, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.395, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.5106192, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.598, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.5187001, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.806, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.5268419, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.024, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.534697, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.264, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.542736, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.495, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.550613, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.744, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.560002, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.974, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.5685208, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.21, "width_percent": 0.276}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.57693, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.486, "width_percent": 0.244}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.57765, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.73, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.5861351, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.875, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.594037, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.088, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.602005, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.305, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.610158, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.518, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.618186, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.74, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.625973, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.962, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.634158, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.206, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.642332, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.451, "width_percent": 0.226}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.643023, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.677, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.651721, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.84, "width_percent": 0.24}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.65244, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.08, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.660695, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.243, "width_percent": 0.204}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.661313, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.446, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.67005, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.591, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.6780121, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.822, "width_percent": 0.222}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.678717, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.044, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.687681, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.207, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.688376, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.438, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.697245, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.583, "width_percent": 0.249}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.697981, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.832, "width_percent": 0.136}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.7060618, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.967, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.714459, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.234, "width_percent": 0.226}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.715161, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.461, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.723289, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.624, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.731349, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.859, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.739223, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.085, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.739949, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.321, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.748734, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.484, "width_percent": 0.24}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.749477, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.724, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.757651, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.887, "width_percent": 0.24}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.758395, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.127, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.766473, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.29, "width_percent": 0.24}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.767215, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.529, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.7757509, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.688, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.783706, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.941, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.784436, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.177, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.792971, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.358, "width_percent": 0.244}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.793723, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.602, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.801781, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.765, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.80253, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.001, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.810855, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.141, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.818834, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.385, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.827321, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.621, "width_percent": 0.249}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.828089, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.87, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.8362558, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.033, "width_percent": 0.272}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.8371, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.304, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.845547, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.449, "width_percent": 0.272}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.846368, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.721, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.855032, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.884, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.862997, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.101, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.870993, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.314, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.8717182, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.549, "width_percent": 0.122}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.8798501, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.671, "width_percent": 0.213}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.880543, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.884, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.891502, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.043, "width_percent": 0.253}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.892299, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.296, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.90065, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.455, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.908686, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.685, "width_percent": 0.149}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.9092112, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.835, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.9178112, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.98, "width_percent": 0.199}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.918446, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.179, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.926531, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.324, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.934911, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.541, "width_percent": 0.235}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.935652, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.776, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.943788, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.939, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.944526, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.17, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.9528491, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.333, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.96069, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.555, "width_percent": 0.226}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.961415, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.781, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.969639, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.958, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.970392, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.189, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.97916, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.365, "width_percent": 0.24}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.979907, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.605, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.987977, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.746, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.988708, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.976, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841957.996969, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.121, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.004859, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.37, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.013331, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.592, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.021496, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.827, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.0332088, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.058, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.041271, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.312, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.049267, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.565, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.057343, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.796, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.0652251, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.018, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.065984, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.249, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.074799, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.434, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.083159, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.67, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.09129, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.896, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.099175, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.136, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.1072528, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.362, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.115525, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.616, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.123714, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.86, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.131784, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.096, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.139948, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.308, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.147814, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.548, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.157587, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.788, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.1654549, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.042, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.173743, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.295, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.181834, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.526, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.189684, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.766, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.198008, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.006, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.205983, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.255, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.213982, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.49, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.221789, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.726, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.230037, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.975, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.238436, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.224, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.24663, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.459, "width_percent": 0.249}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.247413, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.708, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.255517, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.871, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.264412, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.106, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.2729292, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.351, "width_percent": 0.208}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.2736611, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.559, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.28271, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.704, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.290993, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.93, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.2994301, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.184, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.307793, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.406, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.315364, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.596, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.323148, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.781, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.330828, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.967, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.33984, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.193, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.347647, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.356, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.3559449, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.524, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.364564, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.7, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.373123, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.89, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.381403, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.085, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.3896868, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.248, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.397615, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.429, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.4062, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.61, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.414094, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.805, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.42223, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.986, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.430141, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.171, "width_percent": 0.195}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.430823, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.366, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.439868, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.52, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.448349, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.764, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.4564328, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.959, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.4645839, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.149, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.472743, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.344, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.482699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.529, "width_percent": 0.263}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.483535, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.792, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.4921298, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.937, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.500467, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.131, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.508576, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.353, "width_percent": 0.19}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.50923, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.543, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.517877, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.711, "width_percent": 0.204}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.518584, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.915, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.5269802, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.055, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.535574, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.268, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.544099, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.489, "width_percent": 0.231}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.544857, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.72, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.5531871, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.924, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.561647, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.141, "width_percent": 0.249}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.570017, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.39, "width_percent": 0.281}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.5709171, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.671, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.5852652, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.879, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.594016, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.096, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.6020532, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.318, "width_percent": 0.213}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.602782, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.531, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.611487, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.726, "width_percent": 0.253}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.619738, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.979, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.628202, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.196, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.636925, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.427, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.645261, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.617, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.653607, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.789, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.661995, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.975, "width_percent": 0.167}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.6625872, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.143, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.6723201, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.283, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.6805692, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.5, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.689034, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.717, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.6971679, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.921, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.705355, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.107, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.7135599, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.333, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.721858, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.528, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.729801, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.736, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.7381551, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.912, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.74705, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.139, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.755052, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.365, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.763347, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.569, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.771205, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.745, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.780022, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.926, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.7883332, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.194, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.7964108, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.393, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.804436, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.583, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.812432, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.768, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.820396, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.968, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.828906, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.149, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.837067, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.325, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.845517, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.493, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.853625, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.669, "width_percent": 0.149}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.861936, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.819, "width_percent": 0.158}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.862504, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.977, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.871163, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.122, "width_percent": 0.177}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.8717592, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.298, "width_percent": 0.127}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.8799722, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.425, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.888428, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.624, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.8967931, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.801, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.905212, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.946, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.913666, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.127, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9219599, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.294, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.930325, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.471, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9385421, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.611, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9466932, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.752, "width_percent": 0.127}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9548779, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.878, "width_percent": 0.181}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9555151, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.059, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.963636, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.2, "width_percent": 0.163}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9642181, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.363, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9730399, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.503, "width_percent": 0.272}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.981706, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.775, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.990143, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.015, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841958.9984522, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.177, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.006651, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.381, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.01496, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.553, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.0230649, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.77, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.031985, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.952, "width_percent": 0.222}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.032746, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.173, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.041124, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.336, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.049321, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.54, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.0572891, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.726, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.06564, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.925, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.07399, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.101, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.0822449, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.246, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.090331, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.418, "width_percent": 0.136}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.098513, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.554, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.106903, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.722, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.1150959, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.875, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.123233, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.047, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.131332, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.256, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.139354, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.446, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.147282, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.663, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.155793, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.84, "width_percent": 0.308}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.164149, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.147, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.1726542, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.378, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.1806629, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.605, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.188887, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.777, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.1966689, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.967, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.2049482, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.152, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.212913, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.361, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.2209282, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.528, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.22899, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.741, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.237304, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.913, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.245488, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.089, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.2538118, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.252, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.261855, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.433, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.270447, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.596, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.278408, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.823, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.2868378, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.995, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.294785, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.18, "width_percent": 0.24}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.302901, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.42, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3110769, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.597, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3189108, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.782, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3272078, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.959, "width_percent": 0.195}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.327876, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.154, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3361678, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.317, "width_percent": 0.19}, {"sql": "select * from `ec_product_collections` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.336962, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.507, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3478239, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.661, "width_percent": 0.33}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3589401, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.991, "width_percent": 0.208}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.359656, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.199, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.367791, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.358, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3759842, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.539, "width_percent": 0.213}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.376695, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.752, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.3846118, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.914, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.392543, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.091, "width_percent": 0.172}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.393153, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.263, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.401003, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.417, "width_percent": 0.177}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.4016151, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.593, "width_percent": 0.149}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.4097729, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.743, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.417373, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.919, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.425248, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.091, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.432849, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.268, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.44085, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.44, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.448498, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.626, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.456346, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.802, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.463973, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.983, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.4718719, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.155, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.479474, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.336, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.487514, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.508, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.495142, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.685, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.503017, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.857, "width_percent": 0.19}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.510675, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.047, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.518702, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.219, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.526669, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.382, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.535007, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.54, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.543336, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.735, "width_percent": 0.154}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.543937, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.889, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.552638, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.029, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.560914, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.201, "width_percent": 0.154}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.5693111, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.355, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.5776038, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.5, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.585882, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.681, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.5958772, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.844, "width_percent": 0.204}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.596602, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.048, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6058981, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.193, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.614883, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.41, "width_percent": 0.231}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.624431, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.641, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6337152, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.908, "width_percent": 0.222}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6425872, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.13, "width_percent": 0.208}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.650831, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.338, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6590981, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.501, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6672082, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.646, "width_percent": 0.122}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.675547, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.768, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.6835399, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.94, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.691651, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.107, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.699488, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.27, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.708169, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.429, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.716426, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.673, "width_percent": 0.181}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.717073, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.854, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.7254, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.995, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.733033, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.207, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.740959, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.384, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.748575, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.565, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.75644, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.737, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.764053, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.918, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.77495, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.077, "width_percent": 0.217}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.783309, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.294, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.79131, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.466, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.799416, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.624, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.807376, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.792, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.815388, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.95, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.823328, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.113, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.831624, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.272, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.8392599, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.457, "width_percent": 0.181}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.839901, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.638, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.848181, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.797, "width_percent": 0.204}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.8558788, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.863766, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.177, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.871366, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.358, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.879164, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.535, "width_percent": 0.186}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.879826, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.72, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.887697, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.879, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.895989, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.055, "width_percent": 0.177}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.8966331, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.232, "width_percent": 0.136}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.904455, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.368, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.912463, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.544, "width_percent": 0.186}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.9200819, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.73, "width_percent": 0.177}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.920706, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.906, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.9287438, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.065, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.936352, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.246, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.944141, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.422, "width_percent": 0.181}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.944779, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.603, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.95263, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.762, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.960505, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.938, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.968131, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.119, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.9759479, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.291, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.9835548, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.472, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.991361, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.649, "width_percent": 0.186}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.992019, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.835, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841959.9999158, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.993, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.008229, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.17, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.016205, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.342, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.024143, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.505, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.032132, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.672, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.040198, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.844, "width_percent": 0.199}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.047919, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.043, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.055909, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.215, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.063526, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.392, "width_percent": 0.172}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.06415, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.564, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.072213, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.722, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.07974, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.899, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.087456, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.071, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.094985, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.247, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.1029809, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.419, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.110496, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.6, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.118275, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.777, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.1259398, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.958, "width_percent": 0.172}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.1338298, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.13, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.141449, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.307, "width_percent": 0.172}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.142072, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.479, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.150596, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.637, "width_percent": 0.172}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.1512082, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.809, "width_percent": 0.14}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.160159, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.949, "width_percent": 0.371}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.168801, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.321, "width_percent": 0.158}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.176853, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.479, "width_percent": 0.145}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.184755, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.624, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.192831, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.787, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.2007809, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.95, "width_percent": 0.167}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.208803, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.117, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.216464, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.312, "width_percent": 0.177}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.224331, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.488, "width_percent": 0.181}, {"sql": "select exists(select * from `ec_products` where `sku` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.231942, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.67, "width_percent": 0.177}, {"sql": "select * from `ec_product_categories` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749841960.232587, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.846, "width_percent": 0.154}, {"sql": "... 90 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 83, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttributeSet": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttributeSet.php&line=1", "ajax": false, "filename": "ProductAttributeSet.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttribute": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttribute.php&line=1", "ajax": false, "filename": "ProductAttribute.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductCollection": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCollection.php&line=1", "ajax": false, "filename": "ProductCollection.php", "line": "?"}}}, "count": 112, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData", "uri": "POST admin/tools/data-synchronize/import/products/validate", "permission": "ecommerce.import.products.index", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/tools/data-synchronize/import/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:32-68</a>", "middleware": "web, core, auth", "duration": "17.39s", "peak_memory": "72MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-429498473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-429498473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1010732752 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"72 characters\">products_export_2025-06-13_23-07-14-cb2c1a4d2cd1ff40479757ec13b0311f.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4500</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">500</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010732752\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-86892926 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJYYmUzZnFvd1YraDEzL1hFRHZ0dmc9PSIsInZhbHVlIjoiY0pTVkc3YVdwM2I3ZXpzeHZGdmlSbTdpWGY1ay9wOHJuZGVEclJ2YkEyZjdveGVGdThTRENpajhONjZ5dDhVZTNRTjdGTjh4bExlZVNqSDBqWTZHcE1xY1JJMFZUbkw2T1IwQ3VhLzlnVUZHOXFhTHlCRVFQOXlrSDFhVXlSUTIiLCJtYWMiOiI2NWZjNzUwZGE1NmUwNzE5NjI3Y2RlOWI4NjgzM2M2Mzg0YWNjOTlmODg4ODZmMmIzZTAwYzFiNjdhMWM1OGJkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">731</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryxFcSMPnqacD8xja3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJYYmUzZnFvd1YraDEzL1hFRHZ0dmc9PSIsInZhbHVlIjoiY0pTVkc3YVdwM2I3ZXpzeHZGdmlSbTdpWGY1ay9wOHJuZGVEclJ2YkEyZjdveGVGdThTRENpajhONjZ5dDhVZTNRTjdGTjh4bExlZVNqSDBqWTZHcE1xY1JJMFZUbkw2T1IwQ3VhLzlnVUZHOXFhTHlCRVFQOXlrSDFhVXlSUTIiLCJtYWMiOiI2NWZjNzUwZGE1NmUwNzE5NjI3Y2RlOWI4NjgzM2M2Mzg0YWNjOTlmODg4ODZmMmIzZTAwYzFiNjdhMWM1OGJkIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InFvS2tZazRLK25rd01ybXE1UXl2UEE9PSIsInZhbHVlIjoiM0xrcURDOG5JS3k0aC9Ra1BoQitWcmZQUEc3SkxNNU9CejltNWZmQUVzdGYrQXg5Mkc0elZ4cHJFbFIyVXg2eGhqVWRneCtvZE9lci9Fd2x6ejNNUHJkbkRpSml4MUVTbDI0SXRmdEtpRjlac2pzYnBwdnBUSlo4eGZPVExoNWIiLCJtYWMiOiI4MjUwOWFhM2QyOGNlOTBmOTQ1YjJjZDBhMzFjMTdiMmE4NzBmZDAzMDI5ODYyNDE2MjI4N2MzNjM3MjU4OTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86892926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-164710654 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 19:12:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164710654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1059715012 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059715012\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/products/validate", "action_name": "tools.data-synchronize.import.products.validate", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ImportProductController@validateData"}, "badge": null}}