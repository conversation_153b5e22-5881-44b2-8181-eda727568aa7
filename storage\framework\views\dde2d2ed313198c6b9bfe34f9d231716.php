<div class="stores-row">
    

    <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php echo Theme::partial('store-item-grid', compact('store')); ?>

    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/marketplace/includes/store-items.blade.php ENDPATH**/ ?>